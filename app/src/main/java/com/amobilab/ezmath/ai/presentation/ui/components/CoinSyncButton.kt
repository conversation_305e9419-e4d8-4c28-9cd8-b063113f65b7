package com.amobilab.ezmath.ai.presentation.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.app.CoinViewModel
import kotlinx.coroutines.delay

/**
 * Component đơn giản để sync coin với Firebase khi PowerSync gặp vấn đề
 */
@Composable
fun CoinSyncButton(
    modifier: Modifier = Modifier,
    coinViewModel: CoinViewModel = hiltViewModel()
) {
    var isLoading by remember { mutableStateOf(false) }
    var showSuccess by remember { mutableStateOf(false) }
    
    // Tự động ẩn success message sau 2 giây
    LaunchedEffect(showSuccess) {
        if (showSuccess) {
            delay(2000)
            showSuccess = false
        }
    }
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (showSuccess) Color(0xFF38A169).copy(alpha = 0.1f) 
                           else MaterialTheme.colorScheme.surfaceVariant
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = if (showSuccess) "Đồng bộ thành công!" 
                           else "Đồng bộ coin với Firebase",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = if (showSuccess) Color(0xFF38A169) 
                           else MaterialTheme.colorScheme.onSurface
                )
                
                if (!showSuccess) {
                    Text(
                        text = "Nhấn để đồng bộ coin khi PowerSync gặp vấn đề",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            Button(
                onClick = {
                    if (!isLoading) {
                        isLoading = true
                        coinViewModel.performManualSync()
                        
                        // Simulate loading time
                        kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.Main).launch {
                            delay(1500)
                            isLoading = false
                            showSuccess = true
                        }
                    }
                },
                enabled = !isLoading && !showSuccess,
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (showSuccess) Color(0xFF38A169) 
                                   else MaterialTheme.colorScheme.primary
                ),
                shape = RoundedCornerShape(8.dp),
                modifier = Modifier.height(36.dp)
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                } else {
                    Text(
                        text = if (showSuccess) "✓" else "Đồng bộ",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

/**
 * Component nhỏ gọn hơn cho việc hiển thị trong settings
 */
@Composable
fun CoinSyncButtonCompact(
    modifier: Modifier = Modifier,
    coinViewModel: CoinViewModel = hiltViewModel()
) {
    var isLoading by remember { mutableStateOf(false) }
    
    OutlinedButton(
        onClick = {
            if (!isLoading) {
                isLoading = true
                coinViewModel.performManualSync()
                
                kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.Main).launch {
                    delay(1500)
                    isLoading = false
                }
            }
        },
        enabled = !isLoading,
        modifier = modifier.height(32.dp),
        shape = RoundedCornerShape(6.dp)
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(14.dp),
                strokeWidth = 1.5.dp,
                color = MaterialTheme.colorScheme.primary
            )
        } else {
            Text(
                text = "Sync Firebase",
                fontSize = 11.sp
            )
        }
    }
}
