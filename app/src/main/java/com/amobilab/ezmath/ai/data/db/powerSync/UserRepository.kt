package com.amobilab.ezmath.ai.data.db.powerSync

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.debugLog
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.utils.FirestoreUtils
import com.google.firebase.auth.FirebaseAuth
import com.powersync.PowerSyncDatabase
import com.powersync.PowerSyncException
import com.powersync.db.SqlCursor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import java.util.UUID

class UserRepository(private val database: PowerSyncDatabase) {


    // Khởi tạo số dư coin của người dùng nếu nó chưa tồn tại

//    suspend fun initializeUserCoin() {
//        database.writeTransaction { transaction ->
//            try {
//                val id = "1"
//                // Ki<PERSON>m tra xem bản ghi đã tồn tại chưa
//// Kiểm tra xem bản ghi đã tồn tại chưa
//                val existingCoin = transaction.getOptional(
//                    sql = "SELECT coin FROM users WHERE id = ?",
//                    parameters = listOf(id),
//                    mapper = { cursor: SqlCursor ->
//                        cursor.getLong(0)?.toLong() ?: 0L
//                    }
//                )
//                debugLog(
//                    "powerSync initializeUserCoin: existingCoin = $existingCoin"
//                )
//
//                // Nếu bản ghi đã tồn tại, trả về số dư hiện tại
//                if (existingCoin != null) {
//                    return@writeTransaction existingCoin
//                }
//
//                // Nếu bản ghi chưa tồn tại, chèn mới
//                transaction.execute(
//                    sql = """
//                    INSERT INTO users
//                    (id, coin)
//                    VALUES (?, 0)
//                """.trimIndent(),
//                    parameters = listOf(id)
//                )
//                0L // Trả về số dư ban đầu là 0
//            } catch (e: PowerSyncException) {
//                throw RuntimeException("Lỗi khi khởi tạo initializeUserCoin: ${e.message}", e)
//            }
//        }
//    }

    suspend fun updateCoin(coin: Long) = withContext(Dispatchers.IO) {
        debugLog("powerSync updateCoin: coin = $coin")

        try {
            // Cập nhật PowerSync trước
            database.writeTransaction { transaction ->
                transaction.execute(
                    sql = """
                        UPDATE users
                        SET coin = ?
                    """.trimIndent(),
                    parameters = listOf(coin)
                )
            }
            debugLog("PowerSync updateCoin thành công: $coin")

            // Backup lên Firebase sau khi PowerSync thành công
            backupCoinToFirebase(coin)

        } catch (e: PowerSyncException) {
            debugLog("PowerSync updateCoin thất bại: ${e.message}")

            // Nếu PowerSync fail, thử backup lên Firebase
            val firebaseSuccess = backupCoinToFirebase(coin)
            if (firebaseSuccess) {
                debugLog("Firebase backup thành công khi PowerSync fail: $coin")
                // Cập nhật local để đảm bảo UI không bị mất sync
                PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE, coin)
            } else {
                debugLog("Cả PowerSync và Firebase đều fail")
            }

            throw RuntimeException("Lỗi khi cập nhật updateCoin: ${e.message}", e)
        }
    }


    // Theo dõi số coin theo thời gian thực với fallback
    suspend fun getCoin(): Long {
        return try {
            val powerSyncCoin = database.getOptional(
                sql = "SELECT coin FROM users LIMIT 1"
            ) { cursor ->
                cursor.getLong(0)?.toLong() ?: 0L
            } ?: 0L

            debugLog("PowerSync getCoin: $powerSyncCoin")
            powerSyncCoin

        } catch (e: Exception) {
            debugLog("PowerSync getCoin thất bại: ${e.message}, thử restore từ Firebase")

            // Fallback: thử lấy từ Firebase
            try {
                val userId = FirebaseAuth.getInstance().currentUser?.uid
                if (userId != null) {
                    // Sử dụng FirestoreUtils hiện có để lấy coin từ Firebase
                    val localCoin = PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE, 0L)
                    debugLog("Firebase fallback getCoin: $localCoin")
                    localCoin
                } else {
                    debugLog("User chưa đăng nhập, trả về local coin")
                    PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE, 0L)
                }
            } catch (fbError: Exception) {
                debugLog("Firebase getCoin cũng thất bại: ${fbError.message}")
                PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE, 0L)
            }
        }
    }


    // Theo dõi số coin theo thời gian thực
    fun watchCoin(): Flow<Long> {
        return database.watch(
            sql = "SELECT coin FROM users LIMIT 1"
        ) { cursor ->
            cursor.getLong(0)?.toLong() ?: 0L
        }.map { coinList ->
            coinList.firstOrNull() ?: 0L
        }
    }

    suspend fun updateIsDelete(isDelete: Boolean) = withContext(Dispatchers.IO) {

        val isDeleteInt = if (isDelete) 1L else 0L
        debugLog( "updateIsDelete: isDeleteInt = $isDeleteInt")
        database.writeTransaction { transaction ->
            try {
                transaction.execute(
                    sql = """
                        UPDATE users 
                        SET is_deleted = ?
                    """.trimIndent(),
                    parameters = listOf(isDeleteInt)
                )

                // Xóa toàn bộ bản ghi trong chat_table
                transaction.execute(sql = "DELETE FROM chat_table")
                // Xóa toàn bộ bản ghi trong history_table
                transaction.execute(sql = "DELETE FROM history_table")
                // Xóa toàn bộ bản ghi trong coin_history_table (nếu có)
                transaction.execute(sql = "DELETE FROM coin_history_table")


            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi cập nhật updateCoin: ${e.message}", e)
            }
        }
    }


    // Theo dõi số coin theo thời gian thực
    fun watchIsDelete(): Flow<Long> {
        return database.watch(
            sql = "SELECT is_deleted FROM users LIMIT 1"
        ) { cursor ->
            cursor.getLong(0)?.toLong() ?: 0L
        }.map { coinList ->
            coinList.firstOrNull() ?: 0L
        }
    }

    /**
     * Backup coin lên Firebase
     */
    private suspend fun backupCoinToFirebase(coin: Long): Boolean {
        return try {
            val userId = FirebaseAuth.getInstance().currentUser?.uid
            if (userId != null) {
                // Sử dụng FirestoreUtils hiện có để backup
                FirestoreUtils.updateCoinsForUser(userId, coin)
                debugLog("Firebase backup thành công: $coin")
                true
            } else {
                debugLog("User chưa đăng nhập, không thể backup lên Firebase")
                false
            }
        } catch (e: Exception) {
            debugLog("Firebase backup thất bại: ${e.message}")
            false
        }
    }

    /**
     * Sync coin từ Firebase khi PowerSync gặp lỗi
     */
    suspend fun syncCoinFromFirebase(): Long = withContext(Dispatchers.IO) {
        return@withContext try {
            val userId = FirebaseAuth.getInstance().currentUser?.uid
            if (userId != null) {
                // Thực hiện sync với Firebase
                val success = FirestoreUtils.syncCoinsFirestore()
                if (success) {
                    val syncedCoin = PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE, 0L)
                    debugLog("Sync từ Firebase thành công: $syncedCoin")

                    // Thử cập nhật lại PowerSync nếu có thể
                    try {
                        database.writeTransaction { transaction ->
                            transaction.execute(
                                sql = """
                                    INSERT OR REPLACE INTO users (coin, is_deleted)
                                    VALUES (?, 0)
                                """.trimIndent(),
                                parameters = listOf(syncedCoin)
                            )
                        }
                        debugLog("Đã cập nhật PowerSync với coin từ Firebase: $syncedCoin")
                    } catch (e: Exception) {
                        debugLog("Không thể cập nhật PowerSync: ${e.message}")
                    }

                    syncedCoin
                } else {
                    debugLog("Sync Firebase thất bại")
                    0L
                }
            } else {
                debugLog("User chưa đăng nhập")
                0L
            }
        } catch (e: Exception) {
            debugLog("syncCoinFromFirebase error: ${e.message}")
            0L
        }
    }
}
