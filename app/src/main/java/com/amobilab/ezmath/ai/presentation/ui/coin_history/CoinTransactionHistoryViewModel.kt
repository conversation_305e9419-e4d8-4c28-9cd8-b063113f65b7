package com.amobilab.ezmath.ai.presentation.ui.coin_history

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.MixedUtils
import androidx.lifecycle.viewModelScope
import com.amobilab.ezmath.ai.app.BaseViewModel
import com.amobilab.ezmath.ai.data.db.AppDatabase
import com.amobilab.ezmath.ai.data.db.powerSync.CoinHistoryEntityPowerSync
import com.amobilab.ezmath.ai.data.db.powerSync.TransactionType
import com.amobilab.ezmath.ai.data.pref.PrefConst
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.inject.Inject


enum class TransactionFilterType {
    ALL, SPEND, TOP_UP
}

@HiltViewModel
class CoinTransactionHistoryViewModel @Inject constructor() : BaseViewModel() {

    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()

    fun onSearchQueryChanged(query: String) {
        _searchQuery.value = query
    }

    private val _transactionFilter = MutableStateFlow(TransactionFilterType.ALL)
    val transactionFilter: StateFlow<TransactionFilterType> = _transactionFilter.asStateFlow()

    fun onFilterChanged(filter: TransactionFilterType) {
        _transactionFilter.value = filter
    }

    private val _allCoinHistory = MutableStateFlow<List<CoinHistoryEntityPowerSync>>(emptyList())
    val allCoinHistory: StateFlow<List<CoinHistoryEntityPowerSync>> = _allCoinHistory.asStateFlow()

    val filteredTransactions = combine(allCoinHistory, searchQuery, transactionFilter) { list, query, filter ->
        list.filter {
            val matchesQuery = it.description.contains(query, ignoreCase = true)
            val matchesFilter = when (filter) {
                TransactionFilterType.ALL -> true
                TransactionFilterType.SPEND -> it.type == TransactionType.SPEND
                TransactionFilterType.TOP_UP -> it.type == TransactionType.EARN
            }
            matchesQuery && matchesFilter
        }
    }

    val groupedTransactionsByDate = filteredTransactions.map { list ->
        list.groupBy { formatDate(it.date) }
    }

    init {
        watchAllCoinHistory()
    }

    private fun watchAllCoinHistory() {
        viewModelScope.launch {
            AppDatabase.getInstance()
                .getCoinHistoryRepository()
                .watchAllTransactions()
                .collect { transactions ->
                    _allCoinHistory.value = transactions
                }
        }
    }


    fun formatDate(minTime: Long): String {
        val date = Date(minTime)
        val dateFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
        return dateFormat.format(date)
    }

    fun formatTime(minTime: Long): String {
        val date = Date(minTime)
        val timeFormat = DateFormat.getTimeInstance(DateFormat.SHORT, Locale.getDefault())
        return timeFormat.format(date)
    }

}
