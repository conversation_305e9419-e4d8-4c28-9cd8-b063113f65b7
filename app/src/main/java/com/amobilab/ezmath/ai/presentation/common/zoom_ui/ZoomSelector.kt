package com.amobilab.ezmath.ai.presentation.common.zoom_ui

import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.unit.dp
import kotlin.math.PI
import kotlin.math.atan2
import kotlin.math.roundToInt

@Composable
fun ZoomSelector() {
    var currentZoom by remember { mutableStateOf(3.4f) }
    val zoomLevels = listOf(1f, 2f, 3f, 3.4f, 5f, 7f, 10f)

    Box(
        modifier = Modifier
            .size(300.dp)
            .pointerInput(Unit) {
                detectDragGestures { change, _ ->
                    val centerX = size.width / 2f
                    val centerY = size.height.toFloat()
                    val touchX = change.position.x
                    val touchY = change.position.y

                    val angle = atan2(touchY - centerY, touchX - centerX).toDegrees()

                    // Chỉ xử lý góc trong khoảng vòng cung (225 đến -45 độ)
                    val normalizedAngle = if (angle < 0) angle + 360 else angle
                    if (normalizedAngle in 180f..450f) {
                        val fraction = (normalizedAngle - 180f) / 270f
                        val index = (fraction * (zoomLevels.size - 1)).roundToInt().coerceIn(0, zoomLevels.size - 1)
                        currentZoom = zoomLevels[index]
                    }
                }
            }
    ) {
        ZoomDial(
            modifier = Modifier.fillMaxSize(),
            zoomLevels = zoomLevels,
            currentZoom = currentZoom,
            onZoomChange = { currentZoom = it }
        )
    }
}

fun Float.toDegrees() = this * 180f / PI.toFloat()
