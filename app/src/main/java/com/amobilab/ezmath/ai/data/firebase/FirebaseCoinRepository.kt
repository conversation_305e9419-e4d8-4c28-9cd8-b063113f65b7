package com.amobilab.ezmath.ai.data.firebase

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.debugLog
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.SetOptions
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Repository để quản lý coin trên Firebase Firestore
 * Sử dụng như backup khi PowerSync gặp lỗi
 */
class FirebaseCoinRepository {
    
    private val firestore get() = FirebaseFirestore.getInstance("firestoredata")
    private val auth get() = FirebaseAuth.getInstance()
    
    companion object {
        private const val COLLECTION_USERS_COIN = "users_coin"
        private const val FIELD_COINS = "coins"
        private const val FIELD_LAST_UPDATED = "last_updated"
        private const val FIELD_SOURCE = "source" // "powersync" hoặc "firebase"
    }
    
    /**
     * Lấy coin từ Firebase
     */
    suspend fun getCoinFromFirebase(): Long = withContext(Dispatchers.IO) {
        try {
            val userId = getCurrentUserId()
            if (userId.isEmpty()) {
                debugLog("FirebaseCoinRepository: User not logged in")
                return@withContext 0L
            }
            
            val document = firestore.collection(COLLECTION_USERS_COIN)
                .document(userId)
                .get()
                .await()
                
            val coins = document.getLong(FIELD_COINS) ?: 0L
            debugLog("FirebaseCoinRepository: getCoinFromFirebase = $coins")
            return@withContext coins
            
        } catch (e: Exception) {
            debugLog("FirebaseCoinRepository: Error getting coin from Firebase: ${e.message}")
            return@withContext 0L
        }
    }
    
    /**
     * Cập nhật coin lên Firebase
     */
    suspend fun updateCoinToFirebase(coin: Long, source: String = "powersync"): Boolean = withContext(Dispatchers.IO) {
        try {
            val userId = getCurrentUserId()
            if (userId.isEmpty()) {
                debugLog("FirebaseCoinRepository: User not logged in")
                return@withContext false
            }
            
            val coinData = mapOf(
                FIELD_COINS to coin,
                FIELD_LAST_UPDATED to System.currentTimeMillis(),
                FIELD_SOURCE to source
            )
            
            firestore.collection(COLLECTION_USERS_COIN)
                .document(userId)
                .set(coinData, SetOptions.merge())
                .await()
                
            debugLog("FirebaseCoinRepository: updateCoinToFirebase = $coin, source = $source")
            return@withContext true
            
        } catch (e: Exception) {
            debugLog("FirebaseCoinRepository: Error updating coin to Firebase: ${e.message}")
            return@withContext false
        }
    }
    
    /**
     * Sync coin giữa local và Firebase
     * Trả về coin cuối cùng sau khi sync
     */
    suspend fun syncCoinWithFirebase(): Long = withContext(Dispatchers.IO) {
        try {
            val localCoin = PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE, 0L)
            val firebaseCoin = getCoinFromFirebase()
            
            debugLog("FirebaseCoinRepository: syncCoinWithFirebase - local: $localCoin, firebase: $firebaseCoin")
            
            // Nếu Firebase có coin cao hơn, sử dụng Firebase
            if (firebaseCoin > localCoin) {
                PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE, firebaseCoin)
                debugLog("FirebaseCoinRepository: Using Firebase coin: $firebaseCoin")
                return@withContext firebaseCoin
            }
            // Nếu local có coin cao hơn, cập nhật lên Firebase
            else if (localCoin > firebaseCoin) {
                updateCoinToFirebase(localCoin, "local_sync")
                debugLog("FirebaseCoinRepository: Updated Firebase with local coin: $localCoin")
                return@withContext localCoin
            }
            // Nếu bằng nhau, giữ nguyên
            else {
                debugLog("FirebaseCoinRepository: Coins are equal: $localCoin")
                return@withContext localCoin
            }
            
        } catch (e: Exception) {
            debugLog("FirebaseCoinRepository: Error syncing coin: ${e.message}")
            return@withContext PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE, 0L)
        }
    }
    
    /**
     * Backup coin lên Firebase (không thay đổi local)
     */
    suspend fun backupCoinToFirebase(): Boolean = withContext(Dispatchers.IO) {
        try {
            val localCoin = PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE, 0L)
            return@withContext updateCoinToFirebase(localCoin, "backup")
        } catch (e: Exception) {
            debugLog("FirebaseCoinRepository: Error backing up coin: ${e.message}")
            return@withContext false
        }
    }
    
    /**
     * Restore coin từ Firebase khi PowerSync fail
     */
    suspend fun restoreCoinFromFirebase(): Long = withContext(Dispatchers.IO) {
        try {
            val firebaseCoin = getCoinFromFirebase()
            if (firebaseCoin > 0) {
                PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE, firebaseCoin)
                debugLog("FirebaseCoinRepository: Restored coin from Firebase: $firebaseCoin")
            }
            return@withContext firebaseCoin
        } catch (e: Exception) {
            debugLog("FirebaseCoinRepository: Error restoring coin from Firebase: ${e.message}")
            return@withContext 0L
        }
    }
    
    /**
     * Kiểm tra xem có cần sync không dựa trên timestamp
     */
    suspend fun needsSync(): Boolean = withContext(Dispatchers.IO) {
        try {
            val userId = getCurrentUserId()
            if (userId.isEmpty()) return@withContext false
            
            val document = firestore.collection(COLLECTION_USERS_COIN)
                .document(userId)
                .get()
                .await()
                
            if (!document.exists()) return@withContext true
            
            val lastUpdated = document.getLong(FIELD_LAST_UPDATED) ?: 0L
            val currentTime = System.currentTimeMillis()
            
            // Sync nếu chưa update trong 5 phút
            val needsSync = (currentTime - lastUpdated) > (5 * 60 * 1000)
            debugLog("FirebaseCoinRepository: needsSync = $needsSync")
            return@withContext needsSync
            
        } catch (e: Exception) {
            debugLog("FirebaseCoinRepository: Error checking sync need: ${e.message}")
            return@withContext true
        }
    }
    
    private fun getCurrentUserId(): String {
        return auth.currentUser?.uid ?: PrefAssist.getString(PrefConst.USER_ID, "")
    }
}
