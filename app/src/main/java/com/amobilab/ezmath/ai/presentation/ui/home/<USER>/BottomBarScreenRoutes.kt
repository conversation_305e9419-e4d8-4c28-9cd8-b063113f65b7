package com.amobilab.ezmath.ai.presentation.ui.home.values

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.presentation.navigation.ScreenRoutes

enum class BottomBarScreenRoutes(
    val screenRoute: ScreenRoutes,
    @StringRes val titleId: Int,
    @DrawableRes val iconActive: Int,
    @DrawableRes val iconInactive: Int,
) {
    Scan(
        screenRoute = ScreenRoutes.ScanTab(),
        titleId = R.string.txtid_scan,
        iconActive = R.drawable.svg_ic_tab_scan_active,
        iconInactive = R.drawable.svg_ic_tab_scan,
    ),
    AiChat(
        screenRoute = ScreenRoutes.AiChatTab(),
        titleId = R.string.txtid_ai_chat,
        iconActive = R.drawable.svg_ic_tab_ai_chat_active,
        iconInactive = R.drawable.svg_ic_tab_ai_chat,
    ),
    History(
        screenRoute = ScreenRoutes.HistoryTab(),
        titleId = R.string.txtid_history,
        iconActive = R.drawable.svg_ic_tab_history_active,
        iconInactive = R.drawable.svg_ic_tab_history,
    ),
    Settings(
        screenRoute = ScreenRoutes.SettingTab(),
        titleId = R.string.txtid_settings,
        iconActive = R.drawable.svg_ic_tab_setting_active,
        iconInactive = R.drawable.svg_ic_tab_setting,
    )
}