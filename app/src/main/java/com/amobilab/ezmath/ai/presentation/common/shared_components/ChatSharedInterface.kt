package com.amobilab.ezmath.ai.presentation.common.shared_components

import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.RconfAssist
import amobi.module.common.utils.debugLog
import amobi.module.compose.extentions.appClickable
import amobi.module.compose.foundation.AppBox
import amobi.module.compose.foundation.AppBoxCentered
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppIcon
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppRowCentered
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontFamily
import amobi.module.compose.theme.AppFontSize
import android.R.attr.prompt
import android.graphics.Bitmap
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.graphics.drawable.toBitmap
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.AsyncImagePainter
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import coil.size.Size
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.data.pref.RconfConst
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.MainDataViewModel
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.NavigatorViewModel
import com.amobilab.ezmath.ai.presentation.navigation.ScreenRoutes
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlin.math.max

data class SuggestionEvent(val prompt: String, val timestamp: Long = System.currentTimeMillis())

@Composable
fun BottomBarFreeTrialInfo() {
    val navigatorViewModel = NavigatorViewModel.getInstance()

    val mainDataViewModel = hiltViewModel<MainDataViewModel>()
    val coinTotal = mainDataViewModel.coinViewModel.coinTotal.collectAsState().value
    val countFree = PrefAssist.getInt(PrefConst.FREE_CHAT)
    if (true) {  //coinTotal <= 0 || countFree > 0
        AppRowCentered(
            modifier = Modifier
                .padding(vertical = 3.dp)
                .fillMaxWidth(),
        ) {
            //You have 5 free message left. Get Premium
            AppText(
                text = stringResource(R.string.free_message_remain, countFree),
                color = AppColors.current.titleText,
                fontSize = AppFontSize.SMALL,
                fontWeight = FontWeight.W400,
                lineHeight = 16.sp,
            )
            if (RconfAssist.getBoolean(RconfConst.IS_SHOW_IAP)) {
                AppSpacer(4.dp)
                AppText(
                    text = stringResource(R.string.get_coins),
                    color = AppColors.current.getCoinColor,
                    fontSize = AppFontSize.SMALL,
                    fontWeight = FontWeight.W700,
                    lineHeight = 16.sp,
                    modifier = Modifier
                        .appClickable {
                            navigatorViewModel.navigateTo(ScreenRoutes.InAppPurchaseRoute())
                        }
                )
            }
        }
    }
}

@Composable
fun BottomBarInput(
    modifier: Modifier = Modifier,
    backgroundInput: Color = AppColors.current.backgroundEditText,
    background: Color = AppColors.current.sendBarBackground,
    isLoadingData: Boolean = false,
    suggestionEvent: SuggestionEvent = SuggestionEvent(""),
    onSend: (prompt: String, imageUri: String, imageBitmap: Bitmap?) -> Unit, // <-- Callback gửi
) {

    val uriState = remember { MutableStateFlow("") }
    val textState = remember { mutableStateOf(suggestionEvent.prompt) } // <-- Lưu text nhập vào

    LaunchedEffect(suggestionEvent) {
        textState.value = suggestionEvent.prompt
    }

    val imagePicker =
        rememberLauncherForActivityResult(ActivityResultContracts.PickVisualMedia()) { uri: Uri? ->
            uri?.let {
                uriState.value = uri.toString()
            }
        }

    val bitmap = chatPreviewBitmapFromUri(uriState)
    val keyboardController = LocalSoftwareKeyboardController.current

    AppColumn(
        modifier = modifier
            .fillMaxWidth()
            .background(background)
            .padding(vertical = 8.dp, horizontal = 4.dp),
        verticalArrangement = Arrangement.Center,
    ) {
        val interactionSource = remember { MutableInteractionSource() }

        AppRow(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // Icon chọn ảnh
            AppBoxCentered(
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .clickable {
                        imagePicker.launch(
                            PickVisualMediaRequest
                                .Builder()
                                .setMediaType(ActivityResultContracts.PickVisualMedia.ImageOnly)
                                .build()
                        )
                    },
            ) {
                Image(
                    painter = painterResource(R.drawable.ic_library_new),
                    contentDescription = null,
                    colorFilter = ColorFilter.tint(color = AppColors.current.imagePicker),
                    modifier = Modifier
                        .size(24.dp)
                )
            }

            AppColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .background(backgroundInput, shape = RoundedCornerShape(8.dp))
            ) {
                bitmap?.let {
                    AppBox(
                        modifier = Modifier
                            .padding(start = 12.dp, bottom = 4.dp, top = 12.dp)
                            .size(48.dp)
                            .clip(RoundedCornerShape(6.dp)),
                    ) {
                        Image(
                            modifier = Modifier
                                .size(48.dp),
                            contentDescription = stringResource(R.string.picked_image),
                            contentScale = ContentScale.Crop,
                            bitmap = it.asImageBitmap()
                        )
                        AppIcon(
                            icon = R.drawable.ic_edit_text_remove,
                            size = 20.dp,
                            modifier = Modifier
                                .align(Alignment.TopEnd)
                                .clickable {
                                    uriState.value = ""
                                }
                                .padding(2.dp),
                            tint = null
                        )
                    }
                }

                // TextField nhập nội dung
                BasicTextField(
                    value = textState.value,
                    onValueChange = {
                        textState.value = it
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(min = 34.dp, max = 100.dp),
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                    visualTransformation = VisualTransformation.None,
                    interactionSource = interactionSource,
                    textStyle = TextStyle(
                        color = AppColors.current.text,
                        fontSize = AppFontSize.BODY2,
                        fontFamily = AppFontFamily.get(),
                        fontWeight = FontWeight.Normal,
                        lineHeight = 20.sp
                    ),
                    cursorBrush = SolidColor(AppColors.current.text),
                    singleLine = false,
                ) { innerTextField ->
                    TextFieldDefaults.DecorationBox(
                        value = textState.value,
                        innerTextField = innerTextField,
                        visualTransformation = VisualTransformation.None,
                        singleLine = false,
                        enabled = true,
                        interactionSource = interactionSource,
                        contentPadding = PaddingValues(10.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = TextFieldDefaults.colors(
                            focusedIndicatorColor = Color.Transparent,
                            unfocusedIndicatorColor = Color.Transparent,
                            disabledIndicatorColor = Color.Transparent,
                            focusedContainerColor = backgroundInput,
                            unfocusedContainerColor = backgroundInput,
                        ),
                        placeholder = {
                            AppText(
                                text = stringResource(R.string.enter_your_message_here).replace(
                                    "...",
                                    "…"
                                ),
                                color = AppColors.current.textHintInput,
                                fontSize = AppFontSize.BODY2,
                                lineHeight = 20.sp,
                                fontWeight = FontWeight.W400,
                            )
                        }
                    )
                }
            }
            // Nút gửi
            AppBoxCentered(
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .clickable {
                        keyboardController?.hide()
                        if (textState.value.isNotBlank() || uriState.value.isNotBlank()) {
                            onSend(textState.value.trim(), uriState.value, bitmap)
                            textState.value = ""
                            uriState.value = ""
                        }
                    },
            ) {
                Image(
                    painter = painterResource(if (!isLoadingData) R.drawable.svg_ic_send_new else R.drawable.ic_pause),
                    modifier = Modifier.size(24.dp),
                    contentDescription = stringResource(R.string.send_prompt),
                    colorFilter = if (isLoadingData) ColorFilter.tint(color = AppColors.current.text) else null,
                )
            }
        }
    }
}


@Composable
fun chatPreviewBitmapFromUri(uriState: StateFlow<String>): Bitmap? {
    val uri = uriState.collectAsState().value

    val imageState: AsyncImagePainter.State = rememberAsyncImagePainter(
        model = ImageRequest.Builder(LocalContext.current)
            .data(uri)
            .size(Size.ORIGINAL)
            .build()
    ).state

    if (imageState is AsyncImagePainter.State.Success) {
        val originalBitmap = imageState.result.drawable.toBitmap()

        // Giảm độ phân giải xuống Full HD
        val targetWidth = if (!CommFigs.IS_WEAK_DEVICE) 1440 else 1440 / 2
        val targetHeight = if (!CommFigs.IS_WEAK_DEVICE) 810 else 810 / 2

        // Kiểm tra kích thước bitmap gốc
        if (originalBitmap.width > targetWidth || originalBitmap.height > targetHeight) {
            val scaleWidth = targetWidth.toFloat() / originalBitmap.width
            val scaleHeight = targetHeight.toFloat() / originalBitmap.height
            val scaleFinal = max(scaleWidth, scaleHeight)
            return Bitmap.createScaledBitmap(
                originalBitmap,
                (originalBitmap.width * scaleFinal).toInt(),
                (originalBitmap.height * scaleFinal).toInt(),
                !CommFigs.IS_WEAK_DEVICE
            )
        }
        return originalBitmap
    }

    return null
}