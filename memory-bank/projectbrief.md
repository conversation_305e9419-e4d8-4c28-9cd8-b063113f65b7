# EzMath AI - Project Brief

## Project Overview
EzMath AI is an Android application designed to help users solve and learn mathematics using AI technology. The app appears to use AI capabilities to analyze and solve mathematical problems.

## Core Requirements
- Android application built with Kotlin and Jetpack Compose
- AI-powered mathematics problem solving capabilities
- Multiple build flavors (Alpha, Dev, Product)
- Integration with Firebase services (Auth, Analytics, Firestore, VertexAI)
- Support for image capture and processing (likely for capturing math problems)

## Project Goals
- Provide an intuitive user interface for mathematics problem solving
- Enable users to capture math problems through camera
- Deliver accurate AI-powered solutions and explanations
- Support various mathematics topics and problem types

## Technical Stack
- Language: Kotlin
- UI Framework: Jetpack Compose
- Architecture: Likely MVVM with Clean Architecture (based on package structure)
- AI Integration: Firebase VertexAI
- Dependencies: Various AndroidX libraries, Dagger Hilt for DI, Room for local storage

## Additional Features
- User authentication
- Rich text rendering (CommonMark)
- Camera integration
- Possibly in-app purchases (Billing KTX)

## Development Approach
- Modular architecture with multiple modules (amobi_common, amobi_compose, etc.)
- Component-based UI with custom foundation components
- Firebase integration for backend services 