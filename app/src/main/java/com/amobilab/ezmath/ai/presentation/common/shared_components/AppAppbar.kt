package com.amobilab.ezmath.ai.presentation.common.shared_components

import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.extentions.PreviewAssist
import amobi.module.compose.foundation.AppIcon
import amobi.module.compose.foundation.AppRowCentered
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import amobi.module.compose.theme.AppSize
import amobi.module.compose.theme.AppThemeWrapper
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.amobilab.ezmath.ai.R

@AppPreview
@Composable
fun BaseTopAppbarPreview() {
    PreviewAssist.initVariables(LocalContext.current)
    AppThemeWrapper {
        AppAppbar(
            innerPadding = PaddingValues(0.dp),
            title = "haha",
            onBack = {}
        )
    }
}

@Composable
fun AppAppbar(
    modifier: Modifier = Modifier,
    innerPadding: PaddingValues,
    title: String,
    iconStartVisible: Boolean = true,
    color: Color = AppColors.current.text,
    onBack: (() -> Unit)? = null,
) {
    AppRowCentered(
        modifier = modifier
            .fillMaxWidth()
            .height(AppSize.APPBAR_HEIGHT + innerPadding.calculateTopPadding())
            .zIndex(200f)
            .background(color = AppColors.current.actionBarColor)
            .padding(top = innerPadding.calculateTopPadding()),
    ) {
        if (onBack != null && iconStartVisible)
            AppIcon(
//                imageVector = Icons.AutoMirrored.Rounded.ArrowBack,
                imageVector = ImageVector.vectorResource(R.drawable.ic_back),
                tint = color,
                clickZone = AppSize.MIN_TOUCH_SIZE,
            ) {
                onBack.invoke()
            }
        else {
            AppSpacer(AppSize.MIN_TOUCH_SIZE)
        }
        AppText(
            text = title,
            modifier = Modifier
                .weight(1f),
            fontSize = AppFontSize.TITLE,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Start,
            color = color
        )

        AppSpacer(AppSize.MIN_TOUCH_SIZE)
    }
}