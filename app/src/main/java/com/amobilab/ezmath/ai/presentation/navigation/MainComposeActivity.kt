package com.amobilab.ezmath.ai.presentation.navigation

import amobi.module.common.CommApplication
import amobi.module.common.advertisements.interstitial_ad.AdvertsManagerInter
import amobi.module.common.advertisements.reward_ad.AdvertsManagerReward
import amobi.module.common.configs.CommFigs
import amobi.module.common.utils.DebugLogCustom
import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.utils.launchWhenResumed
import amobi.module.common.utils.repeatOnceWhenResumed
import amobi.module.gpdr.GDPRAssist
import amobi.module.gpdr.GDPRConsent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import com.amobilab.ezmath.ai.app.BaseActivity
import com.amobilab.ezmath.ai.configs.AppSecret
import com.amobilab.ezmath.ai.data.pref.AdIds
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.MainLogicViewModel
import com.amobilab.ezmath.ai.utils.AppCheckUtils
import com.amobilab.ezmath.ai.utils.IapAssist
import com.google.firebase.Firebase
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.analytics
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.EnumMap
import javax.inject.Inject
import kotlin.math.round

@AndroidEntryPoint
class MainComposeActivity : BaseActivity() {
    companion object {
        private const val TAG = "HomeActivity"
    }

    @Inject
    lateinit var appNavigator: AppNavigator
    private val mainLogicViewModel: MainLogicViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        this.repeatOnceWhenResumed {
            // Initialize the secret first time
            AppSecret.initializeSecret(this@MainComposeActivity)
            // Initialize IAP
            IapAssist.instance.connect()
        }

        initGDPR()

        if (CommFigs.IS_DEBUG) {
            AppCheckUtils.requestTestProtectedApi(object : AppCheckUtils.ApiTestCallback {
                override fun onSuccess(response: String?) {
                    // Handle success, e.g. show response in UI
                }

                override fun onFailure(error: String?) {
                    // Handle error, e.g. show error message
                }
            })
        }
    }

    private fun initGDPR() {
        GDPRConsent(this) {
            val gpdrIsEnabled = GDPRAssist.isGDPR()
            val gpdrCanShowAds = if (gpdrIsEnabled) GDPRAssist.canShowAds() else true
            val gpdrCanShowPersonalizedAds = if (gpdrIsEnabled) GDPRAssist.canShowPersonalizedAds() else true
            if (CommFigs.IS_DEBUG) {
                DebugLogCustom.logd("GDPRConsent enabled:  $gpdrIsEnabled")
                DebugLogCustom.logd("GDPRConsent showAds:  $gpdrCanShowAds")
                DebugLogCustom.logd("GDPRConsent showPersonalizedAds:  $gpdrCanShowPersonalizedAds")
            }

            Firebase.analytics.setConsent(
                EnumMap<FirebaseAnalytics.ConsentType, FirebaseAnalytics.ConsentStatus>(
                    FirebaseAnalytics.ConsentType::class.java
                ).apply {
                    put(FirebaseAnalytics.ConsentType.ANALYTICS_STORAGE, FirebaseAnalytics.ConsentStatus.GRANTED)
                    put(
                        FirebaseAnalytics.ConsentType.AD_STORAGE,
                        if (gpdrCanShowAds)
                            FirebaseAnalytics.ConsentStatus.GRANTED
                        else FirebaseAnalytics.ConsentStatus.DENIED
                    )
                    put(
                        FirebaseAnalytics.ConsentType.AD_USER_DATA,
                        if (gpdrCanShowAds)
                            FirebaseAnalytics.ConsentStatus.GRANTED
                        else FirebaseAnalytics.ConsentStatus.DENIED
                    )
                    put(
                        FirebaseAnalytics.ConsentType.AD_PERSONALIZATION,
                        if (gpdrCanShowPersonalizedAds)
                            FirebaseAnalytics.ConsentStatus.GRANTED
                        else FirebaseAnalytics.ConsentStatus.DENIED
                    )
                }
            )

            onGDPRCheckComplete()

            return@GDPRConsent Unit
        }
    }

    private fun onGDPRCheckComplete() {
        val currentMillis = System.currentTimeMillis()
        CoroutineScope(Dispatchers.IO).launch {
            // Initialize the Google Mobile Ads SDK on a background thread.
            CommApplication.initAdMob(this@MainComposeActivity) {
                val durationInSeconds = round((System.currentTimeMillis() - currentMillis) / 1000.0)
                FirebaseAssist.instance.logCustomEvent(
                    "admob_init",
                    Bundle().apply {
                        putString("duration", durationInSeconds.toString())
                    }
                )
            }
            runOnUiThread {
                AdvertsManagerReward.requestRewardAdverts(AdIds.REWARD_CREDIT)

                AdvertsManagerInter.requestFullAdverts(
                    listAdsID = AdIds.interOpen(),
                    isOpenApp = true,
                )

                AdvertsManagerInter.requestFullAdverts(
                    listAdsID = AdIds.interAction(),
                    isOpenApp = false,
                )
            }
        }

        mainLogicViewModel.setShowGDPR(false)
    }


    override fun onDestroy() {
        super.onDestroy()

        IapAssist.instance.disconnect()
    }

    @Composable
    override fun MainContentCompose() {
        MainEntryCompose(appNavigator = appNavigator)
    }
}