package com.amobilab.ezmath.ai.presentation.ui.home.values

import androidx.annotation.StringRes
import com.amobilab.ezmath.ai.R

enum class SettingAccountOptions(
    val icon: Int,
    @StringRes val stringsId: Int
) {
    DeleteAccount(
        icon = R.drawable.ic_setting_delete_account,
        stringsId = R.string.delete_account
    ),
    LogOut(
        icon = R.drawable.ic_setting_log_out,
        stringsId = R.string.log_out
    ),
}