package com.amobilab.ezmath.ai.presentation.ui.scan

import android.content.ContentResolver
import android.content.Context
import android.graphics.Bitmap
import android.graphics.RectF
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asAndroidBitmap
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.utils.BitmapUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject

@HiltViewModel
class ScanViewModel @Inject constructor() : ViewModel() {

    val modeScanFromActivity: MutableLiveData<ChatQuestionMode> by lazy {
        MutableLiveData<ChatQuestionMode>(ChatQuestionMode.Math)
    }
    val imageBitmapFromActivity: MutableLiveData<Bitmap> by lazy {
        MutableLiveData<Bitmap>()
    }

    val rectFFromActivity: MutableLiveData<RectF> by lazy {
        MutableLiveData<RectF>()
    }

    fun fetchOpenScan(context: Context, scanMode: ChatQuestionMode, urlBitmap: String, rectF: RectF) {
        val frameRect = RectF()
        frameRect.left = rectF.left
        frameRect.top = rectF.top
        frameRect.right = rectF.right
        frameRect.bottom = rectF.bottom

        val contentResolver: ContentResolver = context.contentResolver

        val imageBitmap: ImageBitmap? =

            BitmapUtils.getBitmapFromUriOrFile(contentResolver, urlBitmap)

        val resizedBitmap =
            if (imageBitmap != null)
                BitmapUtils.resizeImageBitmapToStandardSize(imageBitmap.asAndroidBitmap())
            else null

        modeScanFromActivity.value = scanMode
        imageBitmapFromActivity.value = resizedBitmap
        rectFFromActivity.value = frameRect
    }

    var cropImageBitmap by mutableStateOf<ImageBitmap?>(null)
        private set

    fun saveCropImageBitmap(newCropImageBitmap: ImageBitmap) {
        cropImageBitmap = newCropImageBitmap
    }

    fun saveImageToCache(context: Context, bitmap: ImageBitmap): String {
        // Lưu Bitmap vào một file ảnh tạm thời
        val tempFile = File.createTempFile("temp_image", ".jpg", context.cacheDir)
        bitmap
            .asAndroidBitmap()
            .compress(Bitmap.CompressFormat.JPEG, 100, FileOutputStream(tempFile))

        return tempFile.absolutePath
    }

}