# Hệ thống Backup Coin với Firebase (Hoàn toàn tự động)

## Tổng quan

Hệ thống này cung cấp một giải pháp backup hoàn toàn tự động cho coin management, sử dụng Firebase Firestore như một backup khi PowerSync gặp lỗi hoặc mất kết nối. <PERSON>h<PERSON><PERSON> cần can thiệp thủ công từ user.

## Cách hoạt động

### 1. Automatic Backup (Real-time)
- Mỗi khi cập nhật coin thông qua PowerSync, hệ thống tự động backup lên Firebase
- Nếu PowerSync fail, hệ thống sẽ fallback sang Firebase ngay lập tức
- Auto sync background job chạy mỗi 2 phút để đảm bảo consistency
- Sử dụng FirestoreUtils hiện có để tương thích với code cũ

### 2. Fallback Mechanism
```kotlin
// Trong UserRepository.updateCoin()
try {
    // Cập nhật PowerSync trước
    database.writeTransaction { ... }
    
    // Backup lên Firebase sau khi thành công
    backupCoinToFirebase(coin)
    
} catch (e: PowerSyncException) {
    // Nếu PowerSync fail, backup lên Firebase
    backupCoinToFirebase(coin)
    // Cập nhật local để UI không bị mất sync
    PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE, coin)
}
```

### 3. Recovery từ Firebase
```kotlin
// Trong UserRepository.getCoin()
try {
    // Thử lấy từ PowerSync trước
    return powerSyncCoin
} catch (e: Exception) {
    // Fallback: lấy từ Firebase/local
    return localCoin
}
```

### 2. Auto Sync Background Job
```kotlin
// Trong AppDatabase.init()
startAutoFirebaseSync()

// Background job chạy mỗi 2 phút
autoSyncJob = autoSyncScope.launch {
    while (isActive) {
        if (FirebaseAuth.getInstance().currentUser != null) {
            performFirebaseSync() // Sync PowerSync ↔ Firebase
        }
        delay(120_000L) // 2 phút
    }
}
```

### 3. Conflict Resolution tự động
- So sánh coin giữa PowerSync và Firebase
- Sử dụng coin cao hơn (max strategy)
- Tự động cập nhật cả hai hệ thống để đồng bộ

## Auto Sync trong AppDatabase

### 1. startAutoFirebaseSync()
- Bắt đầu background job khi init database
- Chạy trong CoroutineScope riêng biệt
- Tự động dừng khi logout

### 2. performFirebaseSync()
- So sánh coin giữa PowerSync và Firebase
- Sử dụng coin cao hơn để resolve conflicts
- Cập nhật cả hai hệ thống để đồng bộ

### 3. stopAutoFirebaseSync()
- Tự động gọi khi logout
- Dọn dẹp background jobs

## Methods mới trong UserRepository

### 1. backupCoinToFirebase(coin: Long)
- Backup coin lên Firebase sử dụng FirestoreUtils
- Private method, tự động gọi trong updateCoin()

### 2. syncCoinFromFirebase()
- Sync coin từ Firebase về local
- Thử cập nhật lại PowerSync nếu có thể
- Public method cho manual sync

## Tích hợp với code hiện tại

### 1. Không breaking changes
- Sử dụng FirestoreUtils hiện có
- Không thay đổi API của CoinViewModel
- Tương thích với logic coin hiện tại

### 2. Error handling
- PowerSync fail → Firebase backup
- Firebase fail → Local storage
- UI luôn được cập nhật

### 3. User experience
- Hoàn toàn transparent (user không cần biết gì)
- Không có UI components phức tạp
- Tự động xử lý mọi trường hợp

## Testing

### 1. Test PowerSync failure
```kotlin
// Ngắt kết nối PowerSync
AppDatabase.getInstance().disconnectAndClear()

// Thử cập nhật coin
coinViewModel.updateCoinBalance(100, "test")

// Kiểm tra coin có được backup lên Firebase không
```

### 2. Test Firebase fallback
```kotlin
// Ngắt internet
// Kiểm tra app có hoạt động với local storage không
```

## Monitoring

### Debug logs
- "PowerSync updateCoin thành công/thất bại"
- "Firebase backup thành công/thất bại"
- "Auto Firebase sync started/stopped"
- "Firebase sync - PowerSync: X, Firebase: Y"
- "Updated PowerSync/Firebase with coin: Z"

### User feedback
- Hoàn toàn transparent, không có UI feedback
- Error handling tự động trong background
- User chỉ thấy coin balance luôn chính xác

## Best practices

### 1. Performance
- Backup chỉ khi cần thiết
- Sử dụng existing FirestoreUtils
- Không block UI thread

### 2. Data consistency
- PowerSync là primary source
- Firebase là backup
- Local storage là fallback cuối cùng

### 3. User experience
- Automatic backup transparent
- Manual sync option available
- Clear visual feedback

## Troubleshooting

### Common issues

1. **Coin không sync**
   - Kiểm tra Firebase connection
   - Kiểm tra user authentication
   - Xem debug logs

2. **Manual sync không hoạt động**
   - Kiểm tra user đã đăng nhập chưa
   - Kiểm tra FirestoreUtils.syncCoinsFirestore()

3. **PowerSync và Firebase không consistent**
   - Chạy manual sync
   - Kiểm tra conflict resolution logic

### Debug commands
```kotlin
// Kiểm tra auto sync job
AppDatabase.getInstance().autoSyncJob?.isActive

// Force sync (for testing)
AppDatabase.getInstance().performFirebaseSync()

// Check coin consistency
val powerSyncCoin = userRepository.getCoin()
val firebaseCoin = PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE, 0L)
```

## Future improvements
- Real-time sync với Firebase listeners
- Conflict resolution UI
- Sync status indicator
- Offline queue management
