package com.amobilab.ezmath.ai.data.db.powerSync

import amobi.module.common.utils.debugLog
import com.powersync.PowerSyncDatabase
import com.powersync.PowerSyncException
import com.powersync.db.SqlCursor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import java.util.UUID

class UserRepository(private val database: PowerSyncDatabase) {


    // Khởi tạo số dư coin của người dùng nếu nó chưa tồn tại

//    suspend fun initializeUserCoin() {
//        database.writeTransaction { transaction ->
//            try {
//                val id = "1"
//                // Kiểm tra xem bản ghi đã tồn tại chưa
//// Kiểm tra xem bản ghi đã tồn tại chưa
//                val existingCoin = transaction.getOptional(
//                    sql = "SELECT coin FROM users WHERE id = ?",
//                    parameters = listOf(id),
//                    mapper = { cursor: SqlCursor ->
//                        cursor.getLong(0)?.toLong() ?: 0L
//                    }
//                )
//                debugLog(
//                    "powerSync initializeUserCoin: existingCoin = $existingCoin"
//                )
//
//                // Nếu bản ghi đã tồn tại, trả về số dư hiện tại
//                if (existingCoin != null) {
//                    return@writeTransaction existingCoin
//                }
//
//                // Nếu bản ghi chưa tồn tại, chèn mới
//                transaction.execute(
//                    sql = """
//                    INSERT INTO users
//                    (id, coin)
//                    VALUES (?, 0)
//                """.trimIndent(),
//                    parameters = listOf(id)
//                )
//                0L // Trả về số dư ban đầu là 0
//            } catch (e: PowerSyncException) {
//                throw RuntimeException("Lỗi khi khởi tạo initializeUserCoin: ${e.message}", e)
//            }
//        }
//    }

    suspend fun updateCoin(coin: Long) = withContext(Dispatchers.IO) {
        debugLog( "powerSync updateCoin: coin = $coin")
        database.writeTransaction { transaction ->
            try {
                transaction.execute(
                    sql = """
                        UPDATE users 
                        SET coin = ?
                    """.trimIndent(),
                    parameters = listOf(coin)
                )
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi cập nhật updateCoin: ${e.message}", e)
            }
        }
    }


    // Theo dõi số coin theo thời gian thực
    suspend fun getCoin(): Long {
        return database.getOptional(
            sql = "SELECT coin FROM users LIMIT 1"
        ) { cursor ->
            cursor.getLong(0)?.toLong() ?: 0L
        } ?: 0L
    }


    // Theo dõi số coin theo thời gian thực
    fun watchCoin(): Flow<Long> {
        return database.watch(
            sql = "SELECT coin FROM users LIMIT 1"
        ) { cursor ->
            cursor.getLong(0)?.toLong() ?: 0L
        }.map { coinList ->
            coinList.firstOrNull() ?: 0L
        }
    }

    suspend fun updateIsDelete(isDelete: Boolean) = withContext(Dispatchers.IO) {

        val isDeleteInt = if (isDelete) 1L else 0L
        debugLog( "updateIsDelete: isDeleteInt = $isDeleteInt")
        database.writeTransaction { transaction ->
            try {
                transaction.execute(
                    sql = """
                        UPDATE users 
                        SET is_deleted = ?
                    """.trimIndent(),
                    parameters = listOf(isDeleteInt)
                )

                // Xóa toàn bộ bản ghi trong chat_table
                transaction.execute(sql = "DELETE FROM chat_table")
                // Xóa toàn bộ bản ghi trong history_table
                transaction.execute(sql = "DELETE FROM history_table")
                // Xóa toàn bộ bản ghi trong coin_history_table (nếu có)
                transaction.execute(sql = "DELETE FROM coin_history_table")


            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi cập nhật updateCoin: ${e.message}", e)
            }
        }
    }


    // Theo dõi số coin theo thời gian thực
    fun watchIsDelete(): Flow<Long> {
        return database.watch(
            sql = "SELECT is_deleted FROM users LIMIT 1"
        ) { cursor ->
            cursor.getLong(0)?.toLong() ?: 0L
        }.map { coinList ->
            coinList.firstOrNull() ?: 0L
        }
    }
}
