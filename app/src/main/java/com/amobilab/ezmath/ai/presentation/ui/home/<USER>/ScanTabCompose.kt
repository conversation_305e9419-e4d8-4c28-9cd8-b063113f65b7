package com.amobilab.ezmath.ai.presentation.ui.home.tabs

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.debugLog
import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.extentions.PreviewAssist
import amobi.module.compose.extentions.appClickable
import amobi.module.compose.extentions.gesturesDisabled
import amobi.module.compose.extentions.minHeight
import amobi.module.compose.extentions.minWidth
import amobi.module.compose.foundation.AppBox
import amobi.module.compose.foundation.AppBoxCentered
import amobi.module.compose.foundation.AppButtonText
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppColumnCentered
import amobi.module.compose.foundation.AppGlideImage
import amobi.module.compose.foundation.AppIcon
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import amobi.module.compose.theme.AppSize
import amobi.module.compose.theme.AppThemeWrapper
import android.content.Context
import android.content.Intent
import android.graphics.RectF
import android.net.Uri
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.boundsInWindow
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.presentation.common.share_dialog.CommonDialog
import com.amobilab.ezmath.ai.presentation.common.shared_components.BottomSheetLanguage
import com.amobilab.ezmath.ai.presentation.common.shared_components.CropCustomView
import com.amobilab.ezmath.ai.presentation.common.shared_components.ModelAiModeBottomSheet
import com.amobilab.ezmath.ai.presentation.common.shared_components.ModelTooltip
import com.amobilab.ezmath.ai.presentation.common.shared_components.ZoomToggle
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.MainDataViewModel
import com.amobilab.ezmath.ai.presentation.common.zoom_ui.ArcZoomSlider
import com.amobilab.ezmath.ai.utils.BitmapUtils
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import com.google.accompanist.permissions.shouldShowRationale
import com.skydoves.balloon.ArrowOrientation
import com.skydoves.balloon.ArrowPositionRules
import com.skydoves.balloon.BalloonAnimation
import com.skydoves.balloon.compose.Balloon
import com.skydoves.balloon.compose.BalloonWindow
import com.skydoves.balloon.compose.rememberBalloonBuilder
import com.skydoves.balloon.compose.setBackgroundColor
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine
import kotlin.math.abs

@AppPreview
@Composable
fun ScanTabComposePreview() {
    PreviewAssist.initVariables(LocalContext.current)
    AppThemeWrapper {
        ScanTabCompose(PaddingValues()) { _, _, _, _ ->
        }
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun ScanTabCompose(
    innerPaddingHome: PaddingValues,
    onScanDone: (scanMode: ChatQuestionMode, urlBitmap: String, isFromCamera: Boolean, rectF: RectF) -> Unit
) {
    val context = LocalContext.current
    val mainDataViewModel = hiltViewModel<MainDataViewModel>()

    // State để lưu trữ URI của hình ảnh đã chọn
    var imageUri by remember { mutableStateOf<String?>(null) }

    var selectedModeIndex by remember { mutableStateOf(context.getString(R.string.txtid_math)) }


    // Tạo launcher cho việc chọn hình ảnh
    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.PickVisualMedia()
    ) { uri ->
        val frameRect = mainDataViewModel.frameRect ?: return@rememberLauncherForActivityResult
        imageUri = uri?.toString() ?: return@rememberLauncherForActivityResult
        onScanDone(
            mainDataViewModel.modeFromScan.value,
            imageUri!!,
            false,
            frameRect
        )
    }

    //====================================================================================================

    val cameraPermissionState = rememberPermissionState(android.Manifest.permission.CAMERA)
    var showDialog by remember { mutableStateOf(false) }

    var showBottomSheetModelAiMode by remember { mutableStateOf(false) }
    var selectedModelIcon by remember {
        mutableIntStateOf(
            when (PrefAssist.getString(PrefConst.MODEL_AI)) {
                ModelAiMode.GEMINI.name -> R.drawable.ic_chat_gemini_no_frame
                ModelAiMode.GPT.name -> R.drawable.ic_chat_gpt_no_frame
                else -> R.drawable.ic_chat_gpt_no_frame
            }
        )
    }

    val nameGeminiAi = "Gemini"
    val nameGptAi = stringResource(R.string.chat_gpt)
    var selectedModelName by remember {
        mutableStateOf(
            when (PrefAssist.getString(PrefConst.MODEL_AI)) {
                ModelAiMode.GEMINI.name -> nameGeminiAi
                ModelAiMode.GPT.name -> nameGptAi
                else -> nameGptAi
            }
        )
    }

    LaunchedEffect(Unit) {
        cameraPermissionState.launchPermissionRequest()
    }
    // Kiểm tra trạng thái quyền camera
    LaunchedEffect(cameraPermissionState.status) {
        if (cameraPermissionState.status.isGranted) {
            // Quyền đã được cấp, không cần xử lý thêm
            showDialog = false
        } else if (!cameraPermissionState.status.shouldShowRationale) {
            // Nếu không có lý do để giải thích, không hiển thị dialog
            showDialog = false
        } else if (!showDialog) {
            // Chỉ hiển thị dialog nếu chưa hiển thị trước đó
            showDialog = true
        }
    }

    // Hiển thị Dialog nếu cần
    if (showDialog) {
        CommonDialog(
            title = stringResource(R.string.camera_access),
            message = stringResource(R.string.camera_access_message),
            confirmText = stringResource(R.string.txtid_allow),
            dismissText = stringResource(R.string.txtid_deny),
            onConfirm = {
                cameraPermissionState.launchPermissionRequest()
                showDialog = false
            },
            onDismiss = { showDialog = false },
        )
    }


    var lensFacing by remember { mutableIntStateOf(CameraSelector.LENS_FACING_BACK) }
    var isFlashEnabled by remember { mutableStateOf(false) }


    var progress by remember { mutableFloatStateOf(0.0f) } // Bắt đầu với zoom 1x (progress = 0)

    // Định nghĩa các mức zoom
    val zoomLevels = listOf(0.0f, 0.1f, 0.4f) // Tương ứng với 1x, 2x, 5x

    val lifecycleOwner = LocalLifecycleOwner.current
    val preview = remember { androidx.camera.core.Preview.Builder().build() }
    val previewView = remember { PreviewView(context) }
    val imageCapture = remember { ImageCapture.Builder().build() }
    var cameraControl by remember { mutableStateOf<androidx.camera.core.CameraControl?>(null) }
    var cameraInfo by remember { mutableStateOf<androidx.camera.core.CameraInfo?>(null) }


    var isLongPressed by remember { mutableStateOf(false) }

    // Biến global để lưu tọa độ Scan
    var globalLeftScan by mutableStateOf(0f)
    var globalTopScan by mutableStateOf(0f)
    var globalRightScan by mutableStateOf(0f)
    var globalBottomScan by mutableStateOf(0f)

    // Gọi hàm setupCamera để khởi tạo camera
    setupCamera(
        context = context,
        lifecycleOwner = lifecycleOwner,
        lensFacing = lensFacing,
        preview = preview,
        imageCapture = imageCapture,
        previewView = previewView,
        onCameraControlUpdated = { cameraControl = it },
        onCameraInfoUpdated = { cameraInfo = it }
    )

    // Cập nhật zoom với xử lý mượt mà hơn
    LaunchedEffect(progress) {
        val maxZoomRatio = cameraInfo?.zoomState?.value?.maxZoomRatio ?: 10f
        val minZoomRatio = cameraInfo?.zoomState?.value?.minZoomRatio ?: 1f

        debugLog("maxZoomRatio: $maxZoomRatio, minZoomRatio: $minZoomRatio")

        // Tính toán zoom ratio dựa trên progress
        // Đảm bảo rằng progress = 0.0f tương ứng với minZoomRatio (1x)
        val targetZoomRatio = if (progress <= 0.001f) {
            1.0f // Khi progress gần bằng 0, áp dụng zoom ratio chính xác 1.0f (không zoom)
        } else {
            minZoomRatio + (maxZoomRatio - minZoomRatio) * progress
        }

        debugLog("targetZoomRatio = $targetZoomRatio, progress = $progress")

        // Áp dụng zoom ratio
        cameraControl?.setZoomRatio(targetZoomRatio)
    }

    // Cập nhật trạng thái flash
    LaunchedEffect(isFlashEnabled) {
        cameraControl?.enableTorch(isFlashEnabled)
    }

    // Custom Viewfinder view
    val cropCustomView = remember { CropCustomView(context) }
    cropCustomView.turnOnOffAll(false)
    LaunchedEffect(previewView) {
        delay(100)
        snapshotFlow { previewView.width to previewView.height }
            .collect { (width, height) ->
                if (width > 0 && height > 0) {
                    cropCustomView.turnOnOffAll(false)
                    cropCustomView.showIcon(false)
                    cropCustomView.turnOnOffChangeSize(false)
                    cropCustomView.turnOnOffMove(false)
                    val frameRect = RectF(
                        globalLeftScan,
                        globalTopScan - 20f,
                        globalRightScan,
                        globalBottomScan
                    )
                    cropCustomView.setModeFrame(4)
                    cropCustomView.setFrameRect(frameRect)
                    mainDataViewModel.saveFrameRect(frameRect)
                    cropCustomView.setOnIconCameraRotateClickListener {
                        // Đổi camera trước hoặc sau
                        lensFacing = if (lensFacing == CameraSelector.LENS_FACING_BACK) {
                            cropCustomView.setTurnOnOffIconCamera(true)
                            cropCustomView.setTurnOnOffIconFlash(false)
                            isFlashEnabled = false
                            CameraSelector.LENS_FACING_FRONT
                        } else {
                            cropCustomView.setTurnOnOffIconCamera(false)
                            cropCustomView.setTurnOnOffIconFlash(false)
                            isFlashEnabled = false
                            CameraSelector.LENS_FACING_BACK
                        }
                    }
                    cropCustomView.setOnIconFlashClickListener {
                        if (lensFacing == CameraSelector.LENS_FACING_FRONT) {
                            MixedUtils.showToast(
                                context,
                                R.string.flash_is_not_supported_in_front_camera
                            )
                        } else {
                            // Bật/tắt flash
                            isFlashEnabled = !isFlashEnabled
                            cropCustomView.setTurnOnOffIconFlash(isFlashEnabled)
                            MixedUtils.showToast(context, R.string.flash_toggled)
                        }
                    }
                }
            }
    }

    val selectedLanguage =
        remember { mutableStateOf(PrefAssist.getString(PrefConst.TRANSLATE_TARGET)) }
    val showBottomSheetLanguage = remember { mutableStateOf(false) }

    if (showBottomSheetLanguage.value) {
        BottomSheetLanguage(
            onDismiss = { selected ->
                selected?.let {
                    selectedLanguage.value = it
                    showBottomSheetLanguage.value = false
                    PrefAssist.setString(PrefConst.TRANSLATE_TARGET, it)
                }
                showBottomSheetLanguage.value = false
            }
        )
    }
    // Then replace your Balloon implementation with this:
    var balloonWindow: BalloonWindow? by remember { mutableStateOf(null) }

    val builder = rememberBalloonBuilder {
        setArrowSize(10)
        setArrowPosition(0.8f)
        setMarginLeft(70)
        setMarginRight(16)
        setMarginVertical(2)
        setCornerRadius(8f)
    }
    val coroutineScope = rememberCoroutineScope()

    Scaffold { innerPadding ->
        AppColumn(Modifier.fillMaxSize()) {

            AppBox(modifier = Modifier.fillMaxSize()) {
                // Preview view
                AndroidView(
                    factory = { previewView },
                    modifier = Modifier
                        .fillMaxSize()
                )

                // Custom Viewfinder view
                AndroidView(
                    factory = { cropCustomView },
                    modifier = Modifier.fillMaxSize()
                )

                AppColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(bottom = innerPaddingHome.calculateBottomPadding())
                ) {
                    AppRow(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color(0xFF000000).copy(alpha = 0.3f))
                            .padding(innerPadding)
                            .padding(start = 16.dp, end = 8.dp)
                            .padding(top = 16.dp, bottom = 16.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Balloon(
                            builder = builder.apply {
                                setArrowOrientation(ArrowOrientation.TOP) // Arrow pointing up
                                setArrowPositionRules(ArrowPositionRules.ALIGN_ANCHOR) // Position arrow aligned with anchor
                                setBalloonAnimation(BalloonAnimation.FADE)
                                setDismissWhenClicked(false)
//                                setDismissWhenTouchOutside(true)
                                setBackgroundColor(AppColors.current.tooltipBackground)
                            },
                            onBalloonWindowInitialized = { balloonWindow = it },
                            onComposedAnchor = {
                                val count = PrefAssist.getInt(PrefConst.SHOW_TOOLTIP_TAB_SCAN_COUNTER)
                                if (count < PrefConst.SHOW_TOOLTIP_TAB_SCAN_MAX) {
                                    if (count % 3 == 0)
                                        coroutineScope.launch {
                                            delay(300) // Delay 300ms để layout ổn định sau quảng cáo
                                            balloonWindow?.showAlignBottom()
                                        }
                                    PrefAssist.setInt(PrefConst.SHOW_TOOLTIP_TAB_SCAN_COUNTER, count + 1)
                                }
                            }, // Show below the anchor
                            balloonContent = {
                                ModelTooltip {
                                    balloonWindow?.dismiss()
                                    PrefAssist.setInt(
                                        PrefConst.SHOW_TOOLTIP_TAB_SCAN_COUNTER,
                                        PrefConst.SHOW_TOOLTIP_TAB_SCAN_MAX
                                    )
                                }
                            },
                        ) {
                            AppRow(
                                // chọn model
                                modifier = Modifier
                                    .appClickable {
                                        showBottomSheetModelAiMode = true
                                    }
                                    .border(
                                        width = 1.dp,
                                        color = AppColors.current.border,
                                        shape = RoundedCornerShape(8.dp)
                                    )
                                    .padding(8.dp),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center,
                            ) {
                                // Row content remains the same
                                Image(
                                    painter = painterResource(id = selectedModelIcon),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .size(24.dp)
                                        .padding(4.dp)
                                )
                                AppText(
                                    text = selectedModelName,
                                    fontWeight = FontWeight.W400,
                                    fontSize = AppFontSize.BODY1,
                                    color = AppColors.current.onText
                                )
                                Icon(
                                    painter = painterResource(id = R.drawable.svg_ic_arrow_down),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .size(24.dp)
                                        .padding(4.dp),
                                    tint = AppColors.current.onText
                                )
                            }
                        }
                        if (lensFacing == CameraSelector.LENS_FACING_BACK) {
                            AppIcon(
                                if (isFlashEnabled) R.drawable.svg_bt_flash_on else R.drawable.svg_bt_flash_off,
                                size = 24.dp,
                                tint = AppColors.current.onText,
                            ) {
                                isFlashEnabled = !isFlashEnabled
                            }
                        }
                    }

                    AppSpacer(8.dp)

                    if (!cameraPermissionState.status.isGranted) {
                        AppColumn(
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxWidth()
                                .background(Color.Black),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            AppText(
                                text = stringResource(R.string.camera_permission_is_required),
                                color = Color.White,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier
                                    .fillMaxWidth(0.8f)
                                    .align(Alignment.CenterHorizontally),
                                textAlign = TextAlign.Center
                            )
                            AppSpacer(8.dp)
                            AppButtonText(
                                modifier = Modifier
                                    .minWidth(80.dp)
                                    .minHeight(AppSize.MIN_TOUCH_SIZE),
                                text = stringResource(R.string.go_to_settings),
                                fontSize = AppFontSize.BODY2,
                                contentPadding = PaddingValues(
                                    horizontal = 16.dp,
                                    vertical = 8.dp,
                                ),
                                onClick = {
                                    val intent =
                                        Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                                            data =
                                                Uri.fromParts("package", context.packageName, null)
                                        }
                                    context.startActivity(intent)
                                }
                            )
                        }
                    } else {
                        AppColumn(
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxWidth()
                                .onGloballyPositioned { layoutCoordinates ->
                                    val boundsInWindow = layoutCoordinates.boundsInWindow()

                                    globalLeftScan = boundsInWindow.left
                                    globalTopScan = boundsInWindow.top
                                    globalRightScan = boundsInWindow.right
                                    globalBottomScan = boundsInWindow.bottom

                                    val frameRect = RectF(
                                        globalLeftScan,
                                        globalTopScan - 20f,
                                        globalRightScan,
                                        globalBottomScan
                                    )

                                    cropCustomView.setModeFrame(4)
                                    cropCustomView.setFrameRect(frameRect)
                                    mainDataViewModel.saveFrameRect(frameRect)
                                }
                        ) {
                            if (mainDataViewModel.modeFromScan.value == ChatQuestionMode.Translate) {
                                AppBoxCentered(
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    AppRow(
                                        modifier = Modifier
                                            .background(Color.White, shape = RoundedCornerShape(48.dp))
                                            .padding(vertical = 8.dp, horizontal = 18.dp),
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.Center
                                    ) {
                                        AppText(
                                            text = stringResource(R.string.translate_to),
                                            color = Color(0xFF959EA7),//Color
                                            fontSize = AppFontSize.BODY1,
                                            fontWeight = FontWeight.W400,
                                            lineHeight = 24.sp
                                        )
                                        AppSpacer(8.dp)
                                        AppSpacer(
                                            modifier = Modifier
                                                .width(1.dp)
                                                .height(16.dp)
                                                .background(AppColors.current.divider3)
                                        )
                                        AppSpacer(8.dp)
                                        AppRow( //color
                                            modifier = Modifier
                                                .background(
                                                    Color(0xFFF4F4F4),
                                                    shape = RoundedCornerShape(48.dp)
                                                )
                                                .appClickable {
                                                    showBottomSheetLanguage.value = true
                                                }
                                                .padding(vertical = 4.dp, horizontal = 12.dp),
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.SpaceBetween
                                        ) {
                                            AppText(
                                                text = selectedLanguage.value,
                                                color = Color(0xFF2C3E50),//Color
                                                fontSize = AppFontSize.BODY1,
                                                fontWeight = FontWeight.W500,
                                                lineHeight = 24.sp
                                            )
                                            AppSpacer(4.dp)
                                            AppIcon(
                                                icon = R.drawable.svg_ic_arrow_down_new,
                                                size = 24.dp,
                                                clickZone = 32.dp,
                                                tint = AppColors.current.textHintColor
                                            )
                                        }
                                    }
                                }
                            }
                            AppBox(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .pointerInput(Unit) {
                                        detectTapGestures {
                                            isLongPressed = false
                                        }
                                    },
                                contentAlignment = Alignment.BottomCenter
                            ) {
//                                ScanningLine(modifier = Modifier.fillMaxSize())
                                if (isLongPressed) {
                                    // Lấy maxZoomRatio từ cameraInfo
                                    val maxZoomRatio = cameraInfo?.zoomState?.value?.maxZoomRatio ?: 10f

                                    // Đặt TapToZoomArcSlider vào trong Box
                                    ArcZoomSlider(
                                        currentZoom = if (progress <= 0.001f) 1.0f else progress * 10 + 1.0f, // Chuyển đổi progress sang giá trị zoom
                                        maxZoom = maxZoomRatio,
                                        onZoomChange = {
                                            debugLog("onZoomChange: it = $it")
                                            progress = if (it <= 1.001f) {
                                                0.0f // Khi zoom gần bằng 1x, đặt progress = 0
                                            } else {
                                                (it - 1.0f) / 10f
                                            }
                                        },
                                        // Thêm modifier để ngăn sự kiện lan truyền
                                        modifier = Modifier.gesturesDisabled(false)
                                    )
                                } else {
                                    AppColumnCentered(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                    ) {
                                        ZoomToggle(
                                            modifier = Modifier
                                                .width(160.dp) // Điều chỉnh độ rộng phù hợp,
                                            ,
                                            // Chuyển đổi progress sang giá trị zoom hiển thị
                                            selectedIndex = if (progress <= 0.001f) 1.0f else progress * 10 + 1.0f,
                                            onSelectedChange = { index ->
                                                debugLog("checkindex = $index")
                                                when (index) {
                                                    -1f -> isLongPressed = true // Hiện slider
                                                    0f -> progress = 0.0f // 1x - đảm bảo rằng progress = 0 cho zoom 1x
                                                    1f -> progress = zoomLevels[1] // 2x
                                                    5f -> progress = zoomLevels[2] // 5x
                                                    else -> {
                                                        // Tìm mức zoom gần nhất
                                                        val zoomValue = index / 10f
                                                        var closestZoomLevel = zoomLevels[0]
                                                        var minDiff = Float.MAX_VALUE

                                                        for (level in zoomLevels) {
                                                            val diff = abs(level - zoomValue)
                                                            if (diff < minDiff) {
                                                                minDiff = diff
                                                                closestZoomLevel = level
                                                            }
                                                        }

                                                        progress = closestZoomLevel
                                                    }
                                                }
                                            }
                                        )
                                        AppSpacer(8.dp)
                                    }
                                }
                            }
                        }
                    }
                    // Bottom control buttons
                    AppColumn(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color(0xFF000000).copy(alpha = 0.2f))
                    ) {
                        AppRow(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 24.dp)
                                .padding(top = 12.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            AppIcon(
                                R.drawable.ic_tab_scan_image,
                                size = 48.dp,
                                clickZone = 56.dp,
                                tint = null,
                            ) {
                                launcher.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
                            }

                            AppBoxCentered {
                                AppBoxCentered(
                                    modifier = Modifier.appClickable {
                                        if (cameraPermissionState.status.isGranted) {
                                            val bitmap = previewView.bitmap
                                            val frameRect = mainDataViewModel.frameRect
                                            if (bitmap != null && frameRect != null)
                                                onScanDone(
                                                    mainDataViewModel.modeFromScan.value,
                                                    BitmapUtils.saveBitmapToCache(
                                                        context,
                                                        bitmap,
                                                        "bitmap.png"
                                                    ),
                                                    true,
                                                    frameRect
                                                )
                                        } else {
                                            cameraPermissionState.launchPermissionRequest()
                                            showDialog = false
                                        }
                                    }
                                ) {
                                    AppIcon(
                                        R.drawable.svg_camera_button,
                                        size = 64.dp,
                                        tint = mainDataViewModel.modeFromScan.value.color,
                                    )
                                    AppIcon(
                                        R.drawable.svg_camera_button_border,
                                        size = 64.dp,
                                        tint = null,
                                    )
                                }
                                Image(
                                    painter = painterResource(id = mainDataViewModel.modeFromScan.value.iconId),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .size(32.dp)
                                        .padding(4.dp)
                                )
                            }


                            AppIcon(
                                R.drawable.ic_tab_scan_lenc,
                                size = 48.dp,
                                clickZone = 56.dp,
                                tint = null,
                            ) {
                                lensFacing = if (lensFacing == CameraSelector.LENS_FACING_BACK) {
                                    cropCustomView.setTurnOnOffIconCamera(true)
                                    cropCustomView.setTurnOnOffIconFlash(false)
                                    isFlashEnabled = false
                                    CameraSelector.LENS_FACING_FRONT
                                } else {
                                    cropCustomView.setTurnOnOffIconCamera(false)
                                    cropCustomView.setTurnOnOffIconFlash(false)
                                    isFlashEnabled = false
                                    CameraSelector.LENS_FACING_BACK
                                }
                            }

                        }

                        AppSpacer(8.dp)
                        ScanModeSelector(
                            selectMode = mainDataViewModel.modeFromScan.value
                        ) {
                            mainDataViewModel.modeFromScan.value = it
                            selectedModeIndex =
                                context.getString(mainDataViewModel.modeFromScan.value.titleId)
                        }
                    }
                }
            }
            ModelAiModeBottomSheet(
                showIt = showBottomSheetModelAiMode,
                onDismissRequest = { selected ->
                    selected?.let {
                        selectedModelIcon = it.iconResNoFrame
                        selectedModelName = when (it.name) {
                            ModelAiMode.GEMINI.name -> nameGeminiAi
                            ModelAiMode.GPT.name -> nameGptAi
                            else -> nameGptAi
                        }
                    }
                    showBottomSheetModelAiMode = false
                }
            )
        }
    }
}

@Composable
fun setupCamera(
    context: Context,
    lifecycleOwner: androidx.lifecycle.LifecycleOwner,
    lensFacing: Int,
    preview: androidx.camera.core.Preview,
    imageCapture: ImageCapture,
    previewView: PreviewView,
    onCameraControlUpdated: (androidx.camera.core.CameraControl?) -> Unit,
    onCameraInfoUpdated: (androidx.camera.core.CameraInfo?) -> Unit,
) {
    LaunchedEffect(lensFacing) {
        val cameraProvider = context.getCameraProvider()
        cameraProvider.unbindAll()

        val cameraxSelector = CameraSelector.Builder().requireLensFacing(lensFacing).build()

        try {
            // Try to bind with the given lensFacing value.
            val camera = cameraProvider.bindToLifecycle(
                lifecycleOwner,
                cameraxSelector,
                preview,
                imageCapture
            )
            onCameraControlUpdated(camera.cameraControl)
            onCameraInfoUpdated(camera.cameraInfo)
            preview.surfaceProvider = previewView.surfaceProvider
        } catch (ex: IllegalArgumentException) {
            // If no camera is available for the selected lens facing, try a fallback.
            if (lensFacing != CameraSelector.LENS_FACING_BACK) {
                val fallbackSelector =
                    CameraSelector.Builder().requireLensFacing(CameraSelector.LENS_FACING_BACK)
                        .build()
                try {
                    val camera = cameraProvider.bindToLifecycle(
                        lifecycleOwner,
                        fallbackSelector,
                        preview,
                        imageCapture
                    )
                    onCameraControlUpdated(camera.cameraControl)
                    onCameraInfoUpdated(camera.cameraInfo)
                    preview.surfaceProvider = previewView.surfaceProvider
                } catch (fallbackEx: IllegalArgumentException) {
                    // If even the fallback fails, update the state accordingly.
                    onCameraControlUpdated(null)
                    onCameraInfoUpdated(null)
                    // Optionally, display a UI message to notify the user
                }
            } else {
                // If requested lensFacing was already the back camera, simply update the state.
                onCameraControlUpdated(null)
                onCameraInfoUpdated(null)
                // Optionally, display a UI message to notify the user
            }
        }
    }
}

// Hàm này lấy camera provider từ context
suspend fun Context.getCameraProvider(): ProcessCameraProvider = suspendCoroutine { continuation ->
    ProcessCameraProvider.getInstance(this).also { providerFuture ->
        providerFuture.addListener({
            try {
                val provider = providerFuture.get()
                continuation.resume(provider)
            } catch (e: Exception) {
                // Propagate exceptions so they can be handled by the caller.
                continuation.resumeWithException(e)
            }
        }, ContextCompat.getMainExecutor(this))
    }
}

@Composable
private fun ScanModeSelector(
    modifier: Modifier = Modifier,
    selectMode: ChatQuestionMode = ChatQuestionMode.Math,
    onPageChanged: (ChatQuestionMode) -> Unit = {}
) {
    val screenWidth = LocalConfiguration.current.screenWidthDp.dp
    val itemWidth = screenWidth / 3.8f

    var selectedMode by remember { mutableStateOf(selectMode) }

    AppRow(
        modifier = modifier
            .fillMaxWidth()
            .horizontalScroll(rememberScrollState())
            .padding(horizontal = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        ChatQuestionMode.entries.dropLast(1).forEach { content ->
            val isSelected = content == selectedMode

            AppColumn(
                modifier = Modifier
                    .minWidth(itemWidth)
                    .padding(horizontal = 4.dp, vertical = 16.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(
                        if (!isSelected) Color(0xff101010).copy(alpha = 0.3f) else Color(
                            0xffffffff
                        ).copy(alpha = 0.8f), shape = RoundedCornerShape(8.dp)
                    )
                    .clickable {
                        if (selectedMode != content) {
                            selectedMode = content
                            onPageChanged(content)
                        }
                    }
                    .padding(8.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                AppGlideImage(
                    modifier = Modifier
                        .size(20.dp),
                    resId = content.iconId,
                )
                AppText(
                    text = stringResource(content.titleId),
                    color = if (isSelected) AppColors.current.text else AppColors.current.onText,
                    fontWeight = if (isSelected) FontWeight.W500 else FontWeight.W400,
                    textAlign = TextAlign.Center,
                    fontSize = AppFontSize.BODY2,
                    maxLines = 1,
                )
            }
        }
    }
}
