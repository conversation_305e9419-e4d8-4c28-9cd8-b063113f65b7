package com.amobilab.ezmath.ai.presentation.ui.feature_screen

import amobi.module.common.utils.debugLog
import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import android.content.Context
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.presentation.common.shared_values.SuggestFeature
import com.amobilab.ezmath.ai.presentation.ui.feature_screen.base.FeatureField
import com.amobilab.ezmath.ai.presentation.ui.feature_screen.base.FeatureScreenBase

@AppPreview
@Composable
fun WriteAnEssayScreenPreview() {
    WriteAnEssayScreen(
        onSend = { mode: ChatQuestionMode, prompt: String, imageUri: String ->
            debugLog("onSend: $mode, $prompt, $imageUri")
        }
    )
}

@Composable
fun WriteAnEssayScreen(
    onSend: (mode: ChatQuestionMode, prompt: String, imageUri: String) -> Unit,
) {
    val context = LocalContext.current
    // Define fields for the essay screen
    val fields = listOf(
        FeatureField(
            id = "topic",
            label = stringResource(R.string.label_choose_topic),
            suggestions = SuggestFeature.ChooseATopic.listSuggestions,
            value = ""
        ),
        FeatureField(
            id = "type",
            label = stringResource(R.string.label_essay_type) ,
            suggestions = SuggestFeature.TypeOfEssay.listSuggestions,
            value = ""
        ),
        FeatureField(
            id = "wordCount",
            label = stringResource(R.string.label_word_count),
            suggestions = SuggestFeature.WordCount.listSuggestions,
            value = ""
        ),
        FeatureField(
            id = "language",
            label = stringResource(R.string.label_language_tone),
            suggestions = SuggestFeature.LanguageTone.listSuggestions,
            value = ""
        )
    )

    // Use the base component
    FeatureScreenBase(
        title = stringResource(R.string.feature_card_write_an_essay),
        fields = fields,
        generateButtonText = stringResource(R.string.generate_outline),
        generateOutline = { fieldMap ->
            generateEssayOutline(
                context = context,
                topic = fieldMap["topic"] ?: "",
                type = fieldMap["type"] ?: "",
                wordCount = fieldMap["wordCount"] ?: "",
                language = fieldMap["language"] ?: ""
            )
        },
        onSend = { prompt, imageUri ->
            onSend(ChatQuestionMode.WriteAnEssay, prompt, imageUri)
        },
        contentAboveFields = {
            AppText(
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                text = stringResource(R.string.essay_screen_description),
                color = AppColors.current.text,
                fontSize = AppFontSize.BODY1,
                lineHeight = 24.sp,
                fontWeight = FontWeight.W500
            )
        },
        requiredFields = listOf("topic","type") // Chỉ yêu cầu nhập topic là trường bắt buộc
    )
}

fun generateEssayOutline(
    context: Context,
    topic: String,
    type: String,
    wordCount: String,
    language: String
): String {
    val sb = StringBuilder()

    // Thêm thông tin cơ bản
    sb.append(context.getString(R.string.essay_outline_topic_label, topic)).append("\n")
    sb.append(context.getString(R.string.essay_outline_type_label, type)).append("\n")
    sb.append(context.getString(R.string.essay_outline_word_count_label, wordCount)).append("\n")
    sb.append(context.getString(R.string.essay_outline_language_tone_label, language)).append("\n\n")

    // Thêm tiêu đề outline
    sb.append(context.getString(R.string.essay_outline_suggested_outline_label)).append("\n\n")

    // Phần Introduction
    sb.append(context.getString(R.string.essay_outline_introduction_title)).append("\n")
    sb.append(context.getString(R.string.essay_outline_introduction_topic, topic)).append("\n")
    sb.append(context.getString(R.string.essay_outline_introduction_background)).append("\n")
    sb.append(context.getString(R.string.essay_outline_introduction_thesis)).append("\n\n")

    // Phần Body
    sb.append(context.getString(R.string.essay_outline_body_title)).append("\n")
    sb.append(context.getString(R.string.essay_outline_body_paragraph1)).append("\n")
    sb.append(context.getString(R.string.essay_outline_body_paragraph2)).append("\n")
    sb.append(context.getString(R.string.essay_outline_body_paragraph3)).append("\n\n")

    // Phần Conclusion
    sb.append(context.getString(R.string.essay_outline_conclusion_title)).append("\n")
    sb.append(context.getString(R.string.essay_outline_conclusion_summary)).append("\n")
    sb.append(context.getString(R.string.essay_outline_conclusion_restate)).append("\n")
    sb.append(context.getString(R.string.essay_outline_conclusion_final)).append("\n\n")

    // Phần Notes
    sb.append(context.getString(R.string.essay_outline_notes_title)).append("\n")
    sb.append(context.getString(R.string.essay_outline_notes_tone, language)).append("\n")
    sb.append(context.getString(R.string.essay_outline_notes_wordcount, wordCount)).append("\n")
    sb.append(context.getString(R.string.essay_outline_notes_structure, type))

    return sb.toString()
}