package com.amobilab.ezmath.ai.presentation.ui.home.values

import androidx.annotation.StringRes
import com.amobilab.ezmath.ai.R

enum class SettingDeveloperOptions(
    val icon: Int,
    @StringRes val stringsId: Int
) {
    Feedback(
        icon = R.drawable.ic_setting_feedback_and_suggestion,
        stringsId = R.string.feedback_and_suggestion
    ),
    RateApp(
        icon = R.drawable.ic_setting_rate_app,
        stringsId = R.string.txtid_rate_app
    ),
    Share(
        icon = R.drawable.ic_setting_share,
        stringsId = R.string.txtid_share
    ),
    PrivacyPolicy(
        icon = R.drawable.ic_setting_privacy_policy_new,
        stringsId = R.string.privacy_policy
    ),
    Version(
        icon = R.drawable.ic_setting_version,
        stringsId = R.string.txtid_version
    ),
}