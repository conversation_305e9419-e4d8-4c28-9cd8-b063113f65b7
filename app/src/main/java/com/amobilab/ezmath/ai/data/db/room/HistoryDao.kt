package com.amobilab.ezmath.ai.data.db.room

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update

@Dao
interface HistoryDao {

    @Query("SELECT * FROM history_table WHERE historyId = :historyId LIMIT 1")
    suspend fun getHistoryById(historyId: Long): HistoryEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertHistory(history: HistoryEntity): Long

    @Update
    suspend fun updateHistory(history: HistoryEntity)

    @Delete
    suspend fun deleteHistory(history: HistoryEntity)

    // Hàm xóa nhiều item đã chọn
    @Query("DELETE FROM history_table WHERE historyId IN (:historyIds)")
    suspend fun deleteHistories(historyIds: List<Long>)

    @Query("SELECT * FROM history_table ORDER BY timestamp DESC LIMIT 50")
    suspend fun getAllHistory(): List<HistoryEntity>
}