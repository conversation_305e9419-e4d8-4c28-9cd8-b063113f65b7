<!DOCTYPE html>
<html>
<head>
    <meta
            name="viewport"
            content="initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"/>
    <style>
        #keyboard {
          position: fixed;
          bottom: 0;
          left: 0;
          width: 100%;
          background-color: #f1f1f1;
          box-shadow: 0px -2px 5px 0px rgba(0, 0, 0, 0.1);
        }
        #keyboard .row {
          display: flex;
          justify-content: space-around;
        }

        .key {
          width: 33.33%;
          height: 46px;
          border: 1px solid #ddd;
          text-align: center;
          cursor: pointer;
          position: relative;
          border-radius: 10px;
          margin: 2px;
        }
        .key img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          position: absolute;
          top: 0;
          left: 0;
        }
        .hidden {
          display: none;
        }

        #ans {
          display: inline-block;
          border: none;
          font-size: 38px;
        }
        .container {
          display: flex;
          flex-direction: column;
        }
        .mathjax-editor-display {
          overflow: scroll;
          font-size: 34px;
          color: black;
          width: calc(90vw);
          height: 300px;
          background-color: var(--text-field-background-color) !important;
        }
    </style>
</head>

<body class="body">
<div class="container">
    <div
            aria-hidden="true"
            id="editorInstance"
            class="mathjax-editor-display"
    ></div>

    <div class="div2" id="keyboard">
        <div class="text-t" style="text-align: end;border-bottom: 1px solid;margin-bottom: 8px;">
            <span id="ans">NaN</span></div>
        <!--        <span id="ans">NaN</span>-->
        <div class="default">
            <div class="row">
                <button
                        onclick="input('clear')"
                        class="key"
                        style="background-color: #767676">
                    <img src="images/clear.svg" alt="clear"/>
                </button>
                <button
                        onclick="input('percentSign')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/percent.svg" alt="Percentage"/>
                </button>
                <button
                        onclick="input('7')"
                        class="key"
                        style="background-color: white">
                    <img src="images/7.svg" alt="7"/>
                </button>
                <button
                        onclick="input('8')"
                        class="key"
                        style="background-color: white">
                    <img src="images/8.svg" alt="8"/>
                </button>
                <button
                        onclick="input('9')"
                        class="key"
                        style="background-color: white">
                    <img src="images/9.svg" alt="9"/>
                </button>
                <button
                        onclick="input('divide')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/divide.svg" alt="divide"/>
                </button>
            </div>
            <div class="row">
                <button
                        onclick="input('x')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/x.svg" alt="x"/>
                </button>
                <button
                        onclick="input('sqrt')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/root.svg" alt="root"/>
                </button>
                <button
                        onclick="input('4')"
                        class="key"
                        style="background-color: white">
                    <img src="images/4.svg" alt="4"/>
                </button>
                <button
                        onclick="input('5')"
                        class="key"
                        style="background-color: white">
                    <img src="images/5.svg" alt="5"/>
                </button>
                <button
                        onclick="input('6')"
                        class="key"
                        style="background-color: white">
                    <img src="images/6.svg" alt="6"/>
                </button>
                <button
                        onclick="input('times')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/multiply.svg" alt="multiply"/>
                </button>
            </div>
            <div class="row">
                <button
                        onclick="input('exponential')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/squared.svg" alt="squared"/>
                </button>
                <button
                        onclick="input('frac')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/fraction.svg" alt="fraction"/>
                </button>
                <button
                        onclick="input('1')"
                        class="key"
                        style="background-color: white">
                    <img src="images/1.svg" alt="1"/>
                </button>
                <button
                        onclick="input('2')"
                        class="key"
                        style="background-color: white">
                    <img src="images/2.svg" alt="2"/>
                </button>
                <button
                        onclick="input('3')"
                        class="key"
                        style="background-color: white">
                    <img src="images/3.svg" alt="3"/>
                </button>
                <button
                        onclick="input('-')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/minus.svg" alt="minus"/>
                </button>
            </div>
            <div class="row">
                <button
                        onclick="input('(')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/leftparen.svg" alt="leftparen"/>
                </button>
                <button
                        onclick="input(')')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/rightparen.svg" alt="rightparen"/>
                </button>
                <button
                        onclick="input('0')"
                        class="key"
                        style="background-color: white">
                    <img src="images/0.svg" alt="0"/>
                </button>
                <button
                        onclick="input('.')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/point.svg" alt="point"/>
                </button>
                <button
                        onclick="input('equal')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/equal.svg" alt="equal"/>
                </button>
                <button
                        onclick="input('+')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/plus.svg" alt="plus"/>
                </button>
            </div>
        </div>
        <div class="fx hidden">
            <div class="row">
                <button
                        onclick="input('<')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/lt.svg" alt="lt"/>
                </button>
                <button
                        onclick="input('>')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/gt.svg" alt="gt"/>
                </button>
                <button
                        onclick="input('f(x)')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    f(x)
                </button>
                <button
                        onclick="input('log')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/log.svg" alt="log"/>
                </button>
                <button
                        onclick="input('baseExp')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/e_box.svg" alt="e_box"/>
                </button>
                <button
                        onclick="input('e')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/e.svg" alt="e"/>
                </button>
            </div>
            <div class="row">
                <button
                        onclick="input('sin')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/sin.svg" alt="sin"/>
                </button>
                <button
                        onclick="input('cos')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/cos.svg" alt="cos"/>
                </button>
                <button
                        onclick="input('tan')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/tan.svg" alt="tan"/>
                </button>
                <button
                        onclick="input('pi')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/pi.svg" alt="pi"/>
                </button>
                <button
                        onclick="input('degree')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    <img src="images/degree.png" alt="pi"/>
                </button>
                <button
                        onclick="input('i')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/i.svg" alt="ι"/>
                </button>
            </div>
            <div class="row">
                <button
                        onclick="input('abs')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/absolute.svg" alt="absolute"/>
                </button>
                <button
                        onclick="input('floor')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/floor.svg" alt="floor"/>
                </button>
                <button
                        onclick="input('sum')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/sum.svg" alt="sum"/>
                </button>
                <button
                        onclick="input('prod')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/product.svg" alt="prod"/>
                </button>
                <button
                        onclick="input('factorial')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/factorial.svg" alt="factorial"/>
                </button>
                <button
                        onclick="input('max')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/max.svg" alt="max"/>
                </button>
            </div>
            <div class="row">
                <button
                        onclick="input('derivX')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/derivative_1box.svg" alt="derivative_1box"/>
                </button>
                <button
                        onclick="input('int')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/integral.svg" alt="integral"/>
                </button>
                <button
                        onclick="input('limit')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/lim-.svg" alt="lim-"/>
                </button>
                <button
                        onclick="input('infinity')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/infinity.svg" alt="infinity"/>
                </button>
                <button
                        onclick="input('permutation')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/permutation.svg" alt="permutation"/>
                </button>
                <button
                        onclick="input('gcf')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/gcf.svg" alt="gcf"/>
                </button>
            </div>
        </div>

        <div class="abc hidden">
            <div class="row">
                <button
                        onclick="input('a')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    a
                </button>
                <button
                        onclick="input('b')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    b
                </button>
                <button
                        onclick="input('c')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    c
                </button>
                <button
                        onclick="input('d')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    d
                </button>
                <button
                        onclick="input('e')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    e
                </button>
                <button
                        onclick="input('f')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    f
                </button>
                <button
                        onclick="input('g')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    g
                </button>
                <button
                        onclick="input('h')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    h
                </button>
            </div>
            <div class="row">
                <button
                        onclick="input('i')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    i
                </button>
                <button
                        onclick="input('j')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    j
                </button>
                <button
                        onclick="input('k')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    k
                </button>
                <button
                        onclick="input('l')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    l
                </button>
                <button
                        onclick="input('m')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    m
                </button>
                <button
                        onclick="input('n')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    n
                </button>
                <button
                        onclick="input('o')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    o
                </button>
                <button
                        onclick="input('p')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    p
                </button>
            </div>
            <div class="row">
                <button
                        onclick="input('q')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    q
                </button>
                <button
                        onclick="input('r')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    r
                </button>
                <button
                        onclick="input('s')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    s
                </button>
                <button
                        onclick="input('t')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    t
                </button>
                <button
                        onclick="input('u')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    u
                </button>
                <button
                        onclick="input('v')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    v
                </button>
                <button
                        onclick="input('w')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    w
                </button>
                <button
                        onclick="input('tau')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/tau.svg" alt="tau"/>
                </button>
            </div>
            <div class="row">
                <button
                        onclick="input('alpha')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    α
                </button>
                <button
                        onclick="input('beta')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    β
                </button>
                <button
                        onclick="input('gamma')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    γ
                </button>
                <button
                        onclick="input('pi')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/pi.svg" alt="pi"/>
                </button>
                <button
                        onclick="input('theta')"
                        class="key"
                        style="background: rgba(0, 0, 0, 0.07)">
                    <img src="images/theta.svg" alt="theta"/>
                </button>
                <button
                        onclick="input('x')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    x
                </button>
                <button
                        onclick="input('y')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    y
                </button>
                <button
                        onclick="input('z')"
                        class="key"
                        style="
                background: rgba(0, 0, 0, 0.07);
                text-align: center;
                line-height: 50px;
                font-size: 26px;
              ">
                    z
                </button>
            </div>
        </div>

        <div class="row">
            <button
                    onclick="input('change')"
                    class="key"
                    style="background-color: #767676">
                <img src="images/change.svg" alt="change"/>
            </button>
            <button
                    onclick="input('leftarrow')"
                    class="key"
                    style="background-color: #767676">
                <img src="images/leftarrow.svg" alt="leftarrow"/>
            </button>
            <button
                    onclick="input('rightarrow')"
                    class="key"
                    style="background-color: #767676">
                <img src="images/rightarrow.svg" alt="rightarrow"/>
            </button>
            <button
                    onclick="input('enter')"
                    class="key"
                    style="background-color: #767676">
                <img src="images/enter.svg" alt="enter"/>
            </button>
            <button
                    onclick="input('backspace')"
                    class="key"
                    style="background-color: #767676">
                <img src="images/backspace.svg" alt="backspace"/>
            </button>
            <button onclick="input('sent')" class="key" style="background-color: #3498db">
                <img src="images/solve.svg" alt="solve"/>
            </button>
        </div>
    </div>

    <link rel="stylesheet" media="all" href="./guppy-default.min.css"/>
    <script defer src="./guppy.min.js" charset="utf-8" async></script>
    <script defer src="./keyboard.js" async></script>
</div>
</body>
</html>
