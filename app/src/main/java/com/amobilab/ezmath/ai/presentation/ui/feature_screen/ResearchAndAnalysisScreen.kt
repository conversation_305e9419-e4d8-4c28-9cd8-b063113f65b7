package com.amobilab.ezmath.ai.presentation.ui.feature_screen

import amobi.module.common.utils.debugLog
import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import android.content.Context
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.presentation.common.shared_values.SuggestFeature
import com.amobilab.ezmath.ai.presentation.ui.feature_screen.base.FeatureField
import com.amobilab.ezmath.ai.presentation.ui.feature_screen.base.FeatureScreenBase

@AppPreview
@Composable
fun ResearchAndAnalysisScreenPreview() {
    ResearchAndAnalysisScreen(
        onSend = { mode: ChatQuestionMode, prompt: String, imageUri: String ->
            debugLog("onSend: $mode, $prompt, $imageUri")
        }
    )
}

@Composable
fun ResearchAndAnalysisScreen(
    onSend: (mode: ChatQuestionMode, prompt: String, imageUri: String) -> Unit,
) {
    val context = LocalContext.current
    // Define fields for the research screen
    val fields = listOf(
        FeatureField(
            id = "topic",
            label = stringResource(R.string.label_research_topic),
            suggestions = SuggestFeature.ResearchTopic.listSuggestions,
            value = ""
        ),
        FeatureField(
            id = "goal",
            label = stringResource(R.string.label_research_goal),
            suggestions = SuggestFeature.ResearchGoal.listSuggestions,
            value = ""
        ),
        FeatureField(
            id = "sources",
            label = stringResource(R.string.label_preferred_sources),
            suggestions = SuggestFeature.PreferredSources.listSuggestions,
            value = ""
        ),
        FeatureField(
            id = "depth",
            label = stringResource(R.string.label_depth_length),
            suggestions = SuggestFeature.LengthFormat.listSuggestions,
            value = ""
        ),
        FeatureField(
            id = "academicLevel",
            label = stringResource(R.string.label_academic_level),
            suggestions = SuggestFeature.AcademicLevelResearch.listSuggestions,
            value = ""
        )
    )

    // Use the base component with custom content above fields
    FeatureScreenBase(
        title = stringResource(R.string.feature_card_research_and_analysis),
        fields = fields,
        generateButtonText = stringResource(R.string.generate_outline),
        generateOutline = { fieldMap ->
            generateResearchOutline(
                context = context,
                topic = fieldMap["topic"] ?: "",
                goal = fieldMap["goal"] ?: "",
                sources = fieldMap["sources"] ?: "",
                depth = fieldMap["depth"] ?: "",
                academicLevel = fieldMap["academicLevel"] ?: ""
            )
        },
        onSend = { prompt, imageUri ->
            onSend(ChatQuestionMode.ResearchAndAnalysis, prompt, imageUri)
        },
        contentAboveFields = {
            AppText(
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                text = stringResource(R.string.research_screen_description),
                color = AppColors.current.text,
                fontSize = AppFontSize.BODY1,
                lineHeight = 24.sp,
                fontWeight = FontWeight.W500
            )
        },
        requiredFields = listOf("topic","goal") // Chỉ yêu cầu nhập topic là trường bắt buộc
    )
}

fun generateResearchOutline(
    context: Context,
    topic: String,
    goal: String,
    sources: String,
    depth: String,
    academicLevel: String
): String {
    val sb = StringBuilder()

    // Thêm thông tin cơ bản
    sb.append(context.getString(R.string.research_outline_topic_label, topic)).append("\n")
    sb.append(context.getString(R.string.research_outline_goal_label, goal)).append("\n")
    sb.append(context.getString(R.string.research_outline_sources_label, sources)).append("\n")
    sb.append(context.getString(R.string.research_outline_depth_label, depth)).append("\n")
    sb.append(context.getString(R.string.research_outline_academic_level_label, academicLevel)).append("\n\n")

    // Thêm tiêu đề outline
    sb.append(context.getString(R.string.research_outline_suggested_outline_label)).append("\n\n")

    // Phần Introduction
    sb.append(context.getString(R.string.research_outline_introduction_title)).append("\n")
    sb.append(context.getString(R.string.research_outline_introduction_overview, topic)).append("\n")
    sb.append(context.getString(R.string.research_outline_introduction_importance, academicLevel)).append("\n\n")

    // Phần Objectives
    sb.append(context.getString(R.string.research_outline_objectives_title)).append("\n")
    sb.append(context.getString(R.string.research_outline_objectives_goal, goal)).append("\n\n")

    // Phần Methodology
    sb.append(context.getString(R.string.research_outline_methodology_title)).append("\n")
    sb.append(context.getString(R.string.research_outline_methodology_approach)).append("\n")
    sb.append(context.getString(R.string.research_outline_methodology_sources, sources)).append("\n\n")

    // Phần Key Insights
    sb.append(context.getString(R.string.research_outline_key_insights_title)).append("\n")
    sb.append(context.getString(R.string.research_outline_key_insights_trends)).append("\n")
    sb.append(context.getString(R.string.research_outline_key_insights_citations)).append("\n\n")

    // Phần Conclusion
    sb.append(context.getString(R.string.research_outline_conclusion_title)).append("\n")
    sb.append(context.getString(R.string.research_outline_conclusion_summary)).append("\n")
    sb.append(context.getString(R.string.research_outline_conclusion_implications))

    return sb.toString()
}