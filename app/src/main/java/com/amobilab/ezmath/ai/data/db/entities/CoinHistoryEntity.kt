package com.amobilab.ezmath.ai.data.db.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "coin_history_table")
data class CoinHistoryEntity(
    @PrimaryKey(autoGenerate = true) val id: Long = 0,
    val type: TransactionType,
    val amount: Int, // Sử dụng Int cho coin
    val date: Long,
    val description: String,
)

enum class TransactionType {
    EARN, SPEND
}