package com.amobilab.ezmath.ai.presentation.common.shared_components

import androidx.recyclerview.widget.DiffUtil
import com.amobilab.ezmath.ai.data.models.Message

class MessageDiffCallback(
    private val oldList: List<Message>,
    private val newList: List<Message>,
) : DiffUtil.Callback() {
    override fun getOldListSize(): Int = oldList.size
    override fun getNewListSize(): Int = newList.size

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldList[oldItemPosition].message == newList[newItemPosition].message
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldList[oldItemPosition] == newList[newItemPosition]
    }
}
