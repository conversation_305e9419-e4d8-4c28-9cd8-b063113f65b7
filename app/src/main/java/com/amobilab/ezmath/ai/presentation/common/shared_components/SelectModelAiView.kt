package com.amobilab.ezmath.ai.presentation.common.shared_components

import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.RconfAssist
import amobi.module.compose.extentions.clipCorner
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.data.pref.RconfConst
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode
import com.amobilab.ezmath.ai.values.Const

@Composable
fun SelectModelAiView(modifier: Modifier) {
    if (RconfAssist.getInt(RconfConst.SHOW_AI_TYPE) != RconfConst.SHOW_AI_BOTH_TYPE)
        return

    val listModelAi = listOf(Const.AiServiceName.GPT, Const.AiServiceName.GEMINI)
    var selectedTabIndex by remember {
        mutableIntStateOf(
            when (PrefAssist.getString(PrefConst.MODEL_AI)) {
                ModelAiMode.GPT.name -> 0
                ModelAiMode.GEMINI.name -> 1
                else -> 1
            }
        )
    }

    // Tab bar
    TabRow(
        selectedTabIndex = selectedTabIndex,
        modifier = modifier,
        containerColor = Color.White,
        indicator = { tabPositions ->
            AppSpacer(
                modifier = Modifier
                    .tabIndicatorOffset(tabPositions[selectedTabIndex])
                    .fillMaxWidth()
                    .height(4.dp)
                    .clipCorner(20.dp)
//                    .background(MaterialTheme.colorScheme.primary)
                    .background(AppColors.current.primary)
            )
        }
    ) {
        listModelAi.forEachIndexed { index, currentTab ->
            val isHighlight = selectedTabIndex == index
            Tab(
                modifier = Modifier
                    .height(44.dp)
                    .background(AppColors.current.backgroundContentChatBox),
                selected = isHighlight,
                onClick = {
                    selectedTabIndex = index
                    when (index) {
                        0 -> PrefAssist.setString(PrefConst.MODEL_AI, ModelAiMode.GPT.name)
                        1 -> PrefAssist.setString(PrefConst.MODEL_AI, ModelAiMode.GEMINI.name)
                    }
                },
                selectedContentColor = AppColors.current.backgroundEditText,
                unselectedContentColor = Color.Transparent,
                text = {
                    AppText(
                        text = currentTab,
                        fontSize = if (isHighlight) 14.sp else 14.sp,
                        lineHeight = 16.sp,
                        fontWeight = if (isHighlight) FontWeight.Bold else FontWeight.Medium,
                        color = if (isHighlight) AppColors.current.text else AppColors.current.textHintColor
                    )
                }
            )
        }
    }
}