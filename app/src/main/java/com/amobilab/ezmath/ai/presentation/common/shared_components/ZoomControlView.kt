package com.amobilab.ezmath.ai.presentation.common.shared_components

import amobi.module.compose.foundation.AppRow
import android.content.res.Configuration
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Slider
import androidx.compose.material.SliderDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.amobilab.ezmath.ai.R

@Composable
fun ZoomControlView(
    zoomOutIcon: Painter,
    zoomInIcon: Painter,
    onZoomOutClick: () -> Unit,
    onZoomInClick: () -> Unit,
    onSliderValueChange: (Float) -> Unit,
    currentZoom: Float,
    modifier: Modifier = Modifier,
) {

    val zoomOutTint = if (currentZoom == 0.1f) Color.White.copy(0.4f) else Color.White
    val zoomInTint = if (currentZoom == 1f) Color.White.copy(0.4f) else Color.White

    AppRow(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 24.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(
            onClick = onZoomOutClick,
            modifier = Modifier.size(48.dp)
        ) {
            Icon(
                painter = zoomOutIcon,
                contentDescription = "zoom out",
                modifier = Modifier.size(24.dp),
                tint = zoomOutTint
            )
        }

        Slider(
            modifier = Modifier
                .weight(1f),
            value = currentZoom,
            onValueChange = { value -> onSliderValueChange(value) },
            valueRange = 0.1f..1f,
            colors = SliderDefaults.colors(
                thumbColor = MaterialTheme.colorScheme.secondary,
                activeTrackColor = MaterialTheme.colorScheme.secondary
            )
        )

        IconButton(
            onClick = onZoomInClick,
            modifier = Modifier.size(48.dp)
        ) {
            Icon(
                painter = zoomInIcon,
                contentDescription = "Zoom In",
                modifier = Modifier.size(24.dp),
                tint = zoomInTint
            )
        }
    }
}


@Preview(showBackground = true, uiMode = Configuration.UI_MODE_NIGHT_YES)
@Composable
fun ZoomControlViewPreview() {
    ZoomControlView(
        zoomOutIcon = painterResource(R.drawable.svg_ic_zoom_out),
        zoomInIcon = painterResource(R.drawable.svg_ic_zoom_in),
        onZoomOutClick = { },
        onZoomInClick = { },
        onSliderValueChange = {},
        currentZoom = 0.5f
    )
}
