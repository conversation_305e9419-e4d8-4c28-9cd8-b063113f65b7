package com.amobilab.ezmath.ai.data.db.powerSync

import amobi.module.common.utils.debugLog
import com.powersync.PowerSyncDatabase
import com.powersync.PowerSyncException
import com.powersync.db.Queries
import com.powersync.db.SqlCursor
import com.powersync.db.getBytesOptional
import com.powersync.db.getLongOptional
import com.powersync.db.getString
import com.powersync.db.getStringOptional
import com.powersync.db.internal.PowerSyncTransaction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import java.util.Base64
import java.util.UUID

data class HistoryEntityPowerSync(
    val id: String = "",
    val timestamp: Long,
    val historyName: String,
    val isFavorite: Boolean,
    val content: String,
    val imageData: ByteArray?,
    val questionMode: String,
    val modelAiChat: String
)

class HistoryRepository(private val database: PowerSyncDatabase) {

    // Mapper sử dụng các phương thức tiện ích của SqlCursor
    private val HistoryEntityPowerSyncMapper: (SqlCursor) -> HistoryEntityPowerSync = { cursor ->
        val columnMap = cursor.columnNames
        HistoryEntityPowerSync(
            id = cursor.getString(columnMap["id"]?:0) ?: "",
            timestamp = cursor.getLong(columnMap["timestamp"]?:0) ?: 0L,
            historyName = cursor.getStringOptional("history_name") ?: "",
            isFavorite = cursor.getBoolean(columnMap["is_favorite"]?:0) ?: false,
            content = cursor.getStringOptional("content") ?: "",
            imageData = cursor.getBytesOptional("image_data"),
            questionMode = cursor.getStringOptional("question_mode") ?: "",
            modelAiChat = cursor.getStringOptional("model_ai_chat") ?: ""
        )
    }

    // Chèn HistoryEntityPowerSync, trả về historyId
    suspend fun insertHistory(history: HistoryEntityPowerSync): String = withContext(Dispatchers.IO) {
        debugLog( "insertHistory: $history")
        database.writeTransaction { transaction ->
            try {
                val id = UUID.randomUUID().toString()
                transaction.execute(
                    sql = """
                    INSERT OR REPLACE INTO history_table 
                    (id, timestamp, history_name, is_favorite, content, image_data, question_mode, model_ai_chat) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """.trimIndent(),
                    parameters = listOf(
                        id,
                        history.timestamp,
                        history.historyName,
                        if (history.isFavorite) 1 else 0,
                        history.content,
                        history.imageData?.let { encodeToBase64(it) },
                        history.questionMode,
                        history.modelAiChat
                    )
                )
                id
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi chèn HistoryEntityPowerSync: ${e.message}", e)
            }
        }
    }


    // Lấy HistoryEntityPowerSync theo historyId
    suspend fun getHistoryById(historyId: String): HistoryEntityPowerSync? = withContext(Dispatchers.IO) {
        try {
            database.getOptional(
                sql = "SELECT * FROM history_table WHERE id = ? LIMIT 1",
                parameters = listOf(historyId),
                mapper = HistoryEntityPowerSyncMapper
            )
        } catch (e: PowerSyncException) {
            throw RuntimeException("Lỗi khi lấy HistoryEntityPowerSync: ${e.message}", e)
        } catch (e: IllegalArgumentException) {
            throw RuntimeException("Cột không hợp lệ: ${e.message}", e)
        }
    }

    // Lấy tất cả HistoryEntityPowerSync
    suspend fun getAllHistory(): List<HistoryEntityPowerSync> = withContext(Dispatchers.IO) {
        try {
            database.getAll(
                sql = "SELECT * FROM history_table",
                mapper = HistoryEntityPowerSyncMapper
            )
        } catch (e: PowerSyncException) {
            throw RuntimeException("Lỗi khi lấy danh sách HistoryEntityPowerSync: ${e.message}", e)
        } catch (e: IllegalArgumentException) {
            throw RuntimeException("Cột không hợp lệ: ${e.message}", e)
        }
    }

    // Cập nhật HistoryEntityPowerSync
    suspend fun updateHistory(history: HistoryEntityPowerSync) = withContext(Dispatchers.IO) {
        database.writeTransaction { transaction ->
            try {
                transaction.execute(
                    sql = """
                        UPDATE history_table 
                        SET timestamp = ?, history_name = ?, is_favorite = ?, content = ?, image_data = ?, question_mode = ?, model_ai_chat = ?
                        WHERE id = ?
                    """.trimIndent(),
                    parameters = listOf(
                        history.timestamp,
                        history.historyName,
                        if (history.isFavorite) 1 else 0,
                        history.content,
                        history.imageData?.let { encodeToBase64(it) },
                        history.questionMode,
                        history.modelAiChat,
                        history.id
                    )
                )
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi cập nhật HistoryEntityPowerSync: ${e.message}", e)
            }
        }
    }

    // Xóa HistoryEntityPowerSync
    suspend fun deleteHistory(history: HistoryEntityPowerSync) = withContext(Dispatchers.IO) {
        database.writeTransaction { transaction ->
            try {
                // Xóa các bản ghi liên quan trong chat_table (khóa ngoại)
                transaction.execute(
                    sql = "DELETE FROM chat_table WHERE id = ?",
                    parameters = listOf(history.id)
                )
                // Xóa bản ghi trong history_table
                transaction.execute(
                    sql = "DELETE FROM history_table WHERE id = ?",
                    parameters = listOf(history.id)
                )
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi xóa HistoryEntityPowerSync: ${e.message}", e)
            }
        }
    }

    // Xóa nhiều HistoryEntityPowerSync theo danh sách historyId
    suspend fun deleteHistories(historyIds: List<String>) = withContext(Dispatchers.IO) {
        if (historyIds.isEmpty()) return@withContext
        database.writeTransaction { transaction ->
            try {
                val placeholders = historyIds.joinToString(",") { "?" }
                // Xóa các bản ghi liên quan trong chat_table
                transaction.execute(
                    sql = "DELETE FROM chat_table WHERE id IN ($placeholders)",
                    parameters = historyIds
                )
                // Xóa các bản ghi trong history_table
                transaction.execute(
                    sql = "DELETE FROM history_table WHERE id IN ($placeholders)",
                    parameters = historyIds
                )
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi xóa nhiều HistoryEntityPowerSync: ${e.message}", e)
            }
        }
    }

    // Xóa toàn bộ HistoryEntityPowerSync và coin history
    suspend fun deleteAllHistories() = withContext(Dispatchers.IO) {
        debugLog("deleteAllHistories: Xóa toàn bộ lịch sử")
        database.writeTransaction { transaction ->
            try {
                // Xóa toàn bộ bản ghi trong chat_table
                transaction.execute(sql = "DELETE FROM chat_table")
                // Xóa toàn bộ bản ghi trong history_table
                transaction.execute(sql = "DELETE FROM history_table")
                // Xóa toàn bộ bản ghi trong coin_history_table (nếu có)
                transaction.execute(sql = "DELETE FROM coin_history_table")
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi xóa toàn bộ HistoryEntityPowerSync: ${e.message}", e)
            }
        }
    }

    // Theo dõi thay đổi trong history_table theo thời gian thực
    fun watchAllHistory(): Flow<List<HistoryEntityPowerSync>> {
        return database.watch(
            sql = "SELECT * FROM history_table",
            mapper = HistoryEntityPowerSyncMapper
        )
    }

    // Theo dõi các bảng thay đổi (history_table và chat_table)
    fun onTablesChanged(): Flow<Set<String>> {
        return database.onChange(
            tables = setOf("history_table", "chat_table"),
            throttleMs = Queries.DEFAULT_THROTTLE.inWholeMilliseconds,
            triggerImmediately = true
        )
    }

    // Hàm tiện ích: Mã hóa/giải mã ByteArray
    private fun encodeToBase64(bytes: ByteArray): String =
        Base64.getEncoder().encodeToString(bytes)

    private fun decodeFromBase64(str: String): ByteArray =
        Base64.getDecoder().decode(str)
}