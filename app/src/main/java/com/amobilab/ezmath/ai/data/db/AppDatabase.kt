package com.amobilab.ezmath.ai.data.db

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.debugLogTrace
import android.content.Context
import android.util.Base64
import androidx.room.AutoMigration
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.amobilab.ezmath.ai.data.db.powerSync.AppSchema
import com.powersync.PowerSyncDatabase
import com.powersync.db.schema.Column
import com.powersync.db.schema.Index
import com.powersync.db.schema.IndexedColumn
import com.powersync.db.schema.Schema
import com.powersync.db.schema.Table
import com.powersync.db.schema.ColumnType
import com.amobilab.ezmath.ai.data.db.powerSync.ChatRepository
import com.amobilab.ezmath.ai.data.db.powerSync.CoinHistoryRepository
import com.amobilab.ezmath.ai.data.db.powerSync.HistoryRepository
import com.amobilab.ezmath.ai.data.db.powerSync.UserRepository
import com.amobilab.ezmath.ai.data.db.powerSync.MyConnector
import com.amobilab.ezmath.ai.data.db.room.ChatDao
import com.amobilab.ezmath.ai.data.db.room.ChatEntity
import com.amobilab.ezmath.ai.data.db.room.CoinHistoryDao
import com.amobilab.ezmath.ai.data.db.room.CoinHistoryEntity
import com.amobilab.ezmath.ai.data.db.room.HistoryDao
import com.amobilab.ezmath.ai.data.db.room.HistoryEntity
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.powersync.DatabaseDriverFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.UUID


val MIGRATION_2_3 = object : Migration(2, 3) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL(
            "ALTER TABLE history_table ADD COLUMN modelAiChat TEXT NOT NULL DEFAULT ''"
        )
    }
}
@Database(
    entities = [ChatEntity::class, CoinHistoryEntity::class, HistoryEntity::class],
    version = 3,
    exportSchema = true,
    autoMigrations = [
        AutoMigration(from = 1, to = 2),
    ]
)
abstract class RoomAppDatabase : RoomDatabase() {
    abstract fun chatDao(): ChatDao
    abstract fun coinHistoryDao(): CoinHistoryDao
    abstract fun historyDao(): HistoryDao
}

class AppDatabase private constructor() {

    companion object {
        @JvmStatic
        @Synchronized
        fun getInstance() = Holder.instance

        const val POWER_SYNC_DB_NAME = "app_db_powersync"
        const val ROOM_DB_NAME = "app_db"

    }

    private object Holder {
        val instance = AppDatabase()
    }

    private lateinit var powerSyncDb: PowerSyncDatabase
    private lateinit var roomDb: RoomAppDatabase
    private lateinit var historyRepository: HistoryRepository
    private lateinit var chatRepository: ChatRepository
    private lateinit var coinHistoryRepository: CoinHistoryRepository
    private lateinit var userRepository: UserRepository

    fun init(context: Context) {
        if (!::powerSyncDb.isInitialized) {
            // Khởi tạo PowerSyncDatabase
            val driverFactory = DatabaseDriverFactory(context)
            val dbPath = File(context.filesDir, POWER_SYNC_DB_NAME)
            powerSyncDb = PowerSyncDatabase(
                factory = driverFactory,
                schema = AppSchema,
                dbFilename = dbPath.absolutePath
            )

            // Khởi tạo ROOM
            roomDb = Room.databaseBuilder(
                context,
                RoomAppDatabase::class.java,
                ROOM_DB_NAME
            )
            .addMigrations(MIGRATION_2_3)
            .allowMainThreadQueries()
            .build()

            // Khởi tạo các repository
            historyRepository = HistoryRepository(powerSyncDb)
            chatRepository = ChatRepository(powerSyncDb)
            coinHistoryRepository = CoinHistoryRepository(powerSyncDb)
            userRepository = UserRepository(powerSyncDb)
        }
    }

    // Migrate dữ liệu từ ROOM sang PowerSync
    suspend fun migrateFromRoom() = withContext(Dispatchers.IO) {
        if (!::powerSyncDb.isInitialized || !::roomDb.isInitialized) {
            throw IllegalStateException("Database not initialized. Call init() first.")
        }

        // Kiểm tra trạng thái migrate
        if (PrefAssist.getBoolean(PrefConst.KEY_MIGRATION_ROOM_TO_POWER_SYNC_DONE, false) == true) {
            return@withContext
        }

        val totalCoinBalance = PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE, 0)
        debugLogTrace( "Total coin balance to migrate: $totalCoinBalance")

        PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE_OLD, totalCoinBalance)

        val chatDao = roomDb.chatDao()
        val coinHistoryDao = roomDb.coinHistoryDao()
        val historyDao = roomDb.historyDao()


        val chats = chatDao.getAllChats()
        val coinHistories = coinHistoryDao.getAllCoinHistory()
        val histories = historyDao.getAllHistory()

        powerSyncDb.writeTransaction { transaction ->
            // Migrate chat_table
            for (chat in chats) {
                val imageDataBase64 =
                    chat.imageData?.let { Base64.encodeToString(it, Base64.DEFAULT) }
                transaction.execute(
                    """
            INSERT INTO chat_table (id, history_id, timestamp, content, is_human, is_error, image_data, bot_name)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """,
                    listOf(
                        chat.chatListId.toString(), // Tạo id
                        chat.historyId.toString(),
                        chat.timestamp,
                        chat.content,
                        if (chat.isHuman) 1 else 0,
                        if (chat.isError) 1 else 0,
                        imageDataBase64,
                        chat.botName
                    )
                )
            }

            // Migrate coin_history_table
            for (coin in coinHistories) {
                transaction.execute(
                    """
            INSERT INTO coin_history_table (id, type, amount, date, description)
            VALUES (?, ?, ?, ?, ?)
            """,
                    listOf(
                        coin.id.toString(), // Tạo id
                        coin.type.toString(),
                        coin.amount,
                        coin.date,
                        coin.description
                    )
                )
            }

            // Migrate history_table
            for (history in histories) {
                val imageDataBase64 =
                    history.imageData?.let { Base64.encodeToString(it, Base64.DEFAULT) }
                transaction.execute(
                    """
            INSERT INTO history_table (id, timestamp, history_name, is_favorite, content, image_data, question_mode, model_ai_chat)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """,
                    listOf(
                        history.historyId.toString(), // Tạo id
                        history.timestamp,
                        history.historyName,
                        if (history.isFavorite) 1 else 0,
                        history.content,
                        imageDataBase64,
                        history.questionMode,
                        history.modelAiChat
                    )
                )
            }

            // Migrate coin_balance
//            debugLogTrace( "Total coin balance to migrate: $totalCoinBalance")

//            transaction.execute(
//                sql = """
//                        UPDATE users
//                        SET coin = ?
//                    """.trimIndent(),
//                parameters = listOf(totalCoinBalance)
//            )

        }
        // Xóa dữ liệu ROOM sau khi migrate (tùy chọn)
        roomDb.clearAllTables()
        PrefAssist.setBoolean(PrefConst.KEY_MIGRATION_ROOM_TO_POWER_SYNC_DONE, true)
    }

    fun getHistoryRepository(): HistoryRepository {
        check(::historyRepository.isInitialized) { "Database not initialized. Call init() first." }
        return historyRepository
    }

    fun getChatRepository(): ChatRepository {
        check(::chatRepository.isInitialized) { "Database not initialized. Call init() first." }
        return chatRepository
    }

    fun getCoinHistoryRepository(): CoinHistoryRepository {
        check(::coinHistoryRepository.isInitialized) { "Database not initialized. Call init() first." }
        return coinHistoryRepository
    }

    fun getUserRepository(): UserRepository {
        check(::userRepository.isInitialized) { "Database not initialized. Call init() first." }
        return userRepository
    }

    // Phương thức để kết nối với backend
    suspend fun connectToBackend(/* backend configuration */) {
        powerSyncDb.connect(
            connector = MyConnector(),
            crudThrottleMs = 0L,
            retryDelayMs = 100L
        )
    }

    // Phương thức để kết nối với backend
    suspend fun disconnectAndClearForLogout(/* backend configuration */) {
        powerSyncDb.disconnectAndClear()
    }
}