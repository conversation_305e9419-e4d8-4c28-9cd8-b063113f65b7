# Hệ thống Backup Coin với Firebase

## Tổng quan

Hệ thống này cung cấp một giải pháp backup song song cho coin management, sử dụng Firebase Firestore như một backup khi PowerSync gặp lỗi hoặc mất kết nối.

## Kiến trúc

### 1. FirebaseCoinRepository
- **Vị trí**: `app/src/main/java/com/amobilab/ezmath/ai/data/firebase/FirebaseCoinRepository.kt`
- **Chức năng**: Quản lý coin trên Firebase Firestore
- **Các method chính**:
  - `getCoinFromFirebase()`: Lấy coin từ Firebase
  - `updateCoinToFirebase()`: Cập nhật coin lên Firebase
  - `syncCoinWithFirebase()`: Đồng bộ coin giữa local và Firebase
  - `backupCoinToFirebase()`: Backup coin lên Firebase
  - `restoreCoinFromFirebase()`: Restore coin từ Firebase

### 2. CoinSyncManager
- **Vị trí**: `app/src/main/java/com/amobilab/ezmath/ai/data/sync/CoinSyncManager.kt`
- **Chức năng**: Điều phối đồng bộ giữa PowerSync và Firebase
- **Tính năng**:
  - Auto sync định kỳ (30 giây)
  - Auto backup định kỳ (1 phút)
  - Fallback mechanism khi PowerSync fail
  - Conflict resolution

### 3. CoinViewModel (Đã cập nhật)
- **Tích hợp**: CoinSyncManager để xử lý coin operations
- **Tính năng mới**:
  - Auto sync khi khởi tạo
  - Fallback khi PowerSync fail
  - Manual sync method

### 4. UserRepository (Đã cập nhật)
- **Backup tự động**: Mọi thao tác coin đều được backup lên Firebase
- **Fallback**: Khi PowerSync fail, tự động sử dụng Firebase

## Cách hoạt động

### 1. Sync tự động
```kotlin
// CoinSyncManager tự động bắt đầu khi CoinViewModel khởi tạo
coinSyncManager.startAutoSync()

// Sync mỗi 30 giây
// Backup mỗi 1 phút
```

### 2. Cập nhật coin với fallback
```kotlin
// Khi user cập nhật coin
coinSyncManager.updateCoinWithFallback(newCoin, description)

// Thứ tự ưu tiên:
// 1. Cập nhật PowerSync
// 2. Backup lên Firebase
// 3. Nếu PowerSync fail -> sử dụng Firebase
```

### 3. Lấy coin với fallback
```kotlin
// Lấy coin hiện tại
val coin = coinSyncManager.getCurrentCoinWithFallback()

// Thứ tự ưu tiên:
// 1. PowerSync
// 2. Firebase (nếu PowerSync fail)
// 3. Local storage (cuối cùng)
```

## UI Components

### CoinSyncStatusIndicator
- **Vị trí**: `app/src/main/java/com/amobilab/ezmath/ai/presentation/ui/components/CoinSyncStatusIndicator.kt`
- **Chức năng**: Hiển thị trạng thái sync cho user
- **Trạng thái**:
  - `SYNCING`: Đang đồng bộ
  - `SUCCESS`: Đồng bộ thành công
  - `ERROR`: Có lỗi xảy ra
  - `FALLBACK_TO_FIREBASE`: Đang sử dụng Firebase backup

### Cách sử dụng UI Component
```kotlin
@Composable
fun YourScreen() {
    Column {
        CoinSyncStatusIndicator()
        // Nội dung khác...
    }
}
```

## Cấu hình Firebase

### Firestore Collection Structure
```
users_coin/{userId}
├── coins: Long (số coin)
├── last_updated: Long (timestamp)
└── source: String ("powersync" | "firebase" | "local_sync" | "backup")
```

### Security Rules (Khuyến nghị)
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users_coin/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## Error Handling

### 1. PowerSync Connection Issues
- Tự động fallback sang Firebase
- Hiển thị warning cho user
- Retry mechanism

### 2. Firebase Connection Issues
- Sử dụng local storage
- Queue operations để retry sau
- Hiển thị error status

### 3. Conflict Resolution
- Sử dụng coin cao hơn (max strategy)
- Timestamp để xác định dữ liệu mới nhất
- Manual sync option cho user

## Testing

### 1. Test PowerSync Failure
```kotlin
// Ngắt kết nối PowerSync để test fallback
AppDatabase.getInstance().disconnectAndClear()

// Kiểm tra xem coin có được restore từ Firebase không
val coin = coinSyncManager.getCurrentCoinWithFallback()
```

### 2. Test Firebase Failure
```kotlin
// Ngắt kết nối internet
// Kiểm tra xem app có hoạt động với local storage không
```

## Monitoring & Logging

### Debug Logs
- Tất cả operations đều có debug logs
- Prefix: "CoinSyncManager:", "FirebaseCoinRepository:", "UserRepository:"
- Levels: Info, Warning, Error

### Metrics cần theo dõi
- Sync success rate
- Fallback frequency
- Conflict resolution cases
- User experience impact

## Best Practices

### 1. Performance
- Batch operations khi có thể
- Avoid excessive sync calls
- Use appropriate intervals

### 2. Data Consistency
- Always validate coin values
- Handle edge cases (negative coins, etc.)
- Implement proper error recovery

### 3. User Experience
- Show sync status to users
- Provide manual sync option
- Handle offline scenarios gracefully

## Troubleshooting

### Common Issues

1. **Coin không sync**
   - Kiểm tra Firebase connection
   - Kiểm tra user authentication
   - Xem debug logs

2. **Duplicate coin additions**
   - Kiểm tra conflict resolution logic
   - Verify timestamp handling

3. **Performance issues**
   - Adjust sync intervals
   - Optimize Firebase queries
   - Check for memory leaks

### Debug Commands
```kotlin
// Manual sync
coinViewModel.performManualSync()

// Check sync status
val status = coinSyncManager.syncStatus.value

// Force Firebase restore
val coin = userRepository.syncCoinFromFirebase()
```

## Migration Notes

### Từ hệ thống cũ
- Hệ thống mới tương thích với FirestoreUtils hiện tại
- Không cần migration data
- Tự động sync với Firebase existing data

### Future Improvements
- Real-time sync với WebSocket
- Offline queue management
- Advanced conflict resolution
- Analytics integration
