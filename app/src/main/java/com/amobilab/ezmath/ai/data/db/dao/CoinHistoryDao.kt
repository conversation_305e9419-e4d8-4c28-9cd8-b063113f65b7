package com.amobilab.ezmath.ai.data.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import com.amobilab.ezmath.ai.data.db.entities.CoinHistoryEntity

@Dao
interface CoinHistoryDao {

    @Insert
    suspend fun insertTransaction(transaction: CoinHistoryEntity)

    @Query("SELECT * FROM coin_history_table")
    fun getAllTransactions(): List<CoinHistoryEntity>
}