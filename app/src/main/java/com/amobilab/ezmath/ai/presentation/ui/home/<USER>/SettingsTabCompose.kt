package com.amobilab.ezmath.ai.presentation.ui.home.tabs

import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.RconfAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.views.CommActivity
import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.extentions.PreviewAssist
import amobi.module.compose.extentions.appClickable
import amobi.module.compose.extentions.conditional
import amobi.module.compose.extentions.minHeight
import amobi.module.compose.foundation.AppBoxCentered
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppColumnCentered
import amobi.module.compose.foundation.AppDivider
import amobi.module.compose.foundation.AppGlideImage
import amobi.module.compose.foundation.AppIcon
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import amobi.module.compose.theme.AppSize
import amobi.module.compose.theme.AppThemeWrapper
import amobi.module.rate.me.RateMeDialog
import android.annotation.SuppressLint
import android.app.Activity.RESULT_OK
import android.content.ActivityNotFoundException
import android.content.Intent
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.rememberAsyncImagePainter
import com.amobilab.ezmath.ai.BuildConfig
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.data.pref.RconfConst
import com.amobilab.ezmath.ai.presentation.common.share_dialog.CommonDialog
import com.amobilab.ezmath.ai.presentation.common.shared_components.ModelAiModeBottomSheet
import com.amobilab.ezmath.ai.presentation.common.shared_values.AppThemeMode
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.MainDataViewModel
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.NavigatorViewModel
import com.amobilab.ezmath.ai.presentation.navigation.ScreenRoutes
import com.amobilab.ezmath.ai.presentation.ui.home.values.SettingAccountOptions
import com.amobilab.ezmath.ai.presentation.ui.home.values.SettingDeveloperOptions
import com.amobilab.ezmath.ai.presentation.ui.home.values.SettingSystemOptions
import com.amobilab.ezmath.ai.utils.GoogleAuthUiClient
import com.amobilab.ezmath.ai.utils.StringUtils
import com.amobilab.ezmath.ai.values.Const
import com.google.android.gms.auth.api.identity.Identity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Locale
import kotlin.math.roundToLong

@AppPreview
@Composable
fun SettingsTabComposePreview() {
    PreviewAssist.initVariables(LocalContext.current)
    AppThemeWrapper {
        SettingsTabCompose(PaddingValues())
    }
}

@SuppressLint("UseKtx")
@Composable
fun SettingsTabCompose(innerPaddingHome: PaddingValues) {
    val navigatorViewModel = NavigatorViewModel.getInstance()

    val mainDataViewModel = hiltViewModel<MainDataViewModel>()
    var showBottomSheetModelAiMode by remember { mutableStateOf(false) }
    val currentAppTheme by mainDataViewModel.coinViewModel.appThemeMode.observeAsState()
    val context = LocalContext.current

    val coinTotal = mainDataViewModel.coinViewModel.coinTotal.collectAsState().value

    val nameGeminiAi = "Gemini"
    val nameGptAi = stringResource(R.string.chat_gpt)

    var showLogoutDialog by remember { mutableStateOf(false) }



    val coroutineScope = rememberCoroutineScope()

    var isLoading by remember { mutableStateOf(false) }

    val googleAuthUiClient by lazy {
        GoogleAuthUiClient(
            oneTapClient = Identity.getSignInClient(context),
            viewModel = mainDataViewModel
        )
    }

    var signedInUser by remember { mutableStateOf(googleAuthUiClient.getSignedInUser()) }

    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartIntentSenderForResult(),
        onResult = { result ->
            if (result.resultCode == RESULT_OK) {
                isLoading = true
                coroutineScope.launch {
                    val signInResult = googleAuthUiClient.signInWithIntent(
                        intent = result.data ?: return@launch
                    )
                    signedInUser = googleAuthUiClient.getSignedInUser()
//                    viewModel.onSignInResult(signInResult)
                    isLoading = false
                }
            }
        }
    )

    val filteredSettingSystemOptions = SettingSystemOptions.entries
        .let { options ->
            if (RconfAssist.getInt(RconfConst.SHOW_AI_TYPE) != RconfConst.SHOW_AI_BOTH_TYPE) {
                options.filter { it != SettingSystemOptions.AIAssistance }
            } else {
                options
            }
        }
    val funcOnClickSettingSystemItem: (SettingSystemOptions) -> Unit = {
        when (it) {
            SettingSystemOptions.Theme -> {
//                showBottomSheetAppTheme = true
                navigatorViewModel.navigateTo(ScreenRoutes.SetTheme())
            }

            SettingSystemOptions.AIAssistance -> {
                showBottomSheetModelAiMode = true
            }

            SettingSystemOptions.HistoryCoin -> {
                navigatorViewModel.navigateTo(ScreenRoutes.CoinHistory())
            }
        }
    }

    val funcOnClickSettingDeveloperItem: (SettingDeveloperOptions) -> Unit = {
        when (it) {
            SettingDeveloperOptions.Feedback -> {
                navigatorViewModel.navigateTo(ScreenRoutes.FeedbackSuggestion())
            }

            SettingDeveloperOptions.RateApp -> {
                RateMeDialog(
                    context as CommActivity, "FromSetting", "SettingsTab",
                    bgDimAmount = 0.8f,
                ) { isRateButtonOkClicked, rating ->
                    if (rating <= 3 && rating > 0) {
                        navigatorViewModel.navigateTo(ScreenRoutes.FeedbackSuggestion())
                    }
                }.show()
            }

            SettingDeveloperOptions.Share -> {
                mainDataViewModel.shareApp(context)
            }

            SettingDeveloperOptions.PrivacyPolicy -> {
                try {
                    val intent = Intent(Intent.ACTION_VIEW, Const.POLICY_URL.toUri())
                    context.startActivity(intent)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            SettingDeveloperOptions.Version -> {
                val intent = Intent(Intent.ACTION_VIEW)
                try {
                    intent.data = Uri.parse("market://details?id=" + context.packageName)
                    context.startActivity(intent)
                } catch (e: ActivityNotFoundException) {
                    try {
                        intent.data = Uri.parse("https://play.google.com/store/apps/details?id=" + context.packageName)
                        context.startActivity(intent)
                    } catch (_: Exception) {
                    }
                }
            }
        }
    }

    val funcOnClickSettingAccountItem: (SettingAccountOptions) -> Unit = {
        when (it) {
            SettingAccountOptions.DeleteAccount -> {
                if (signedInUser != null) {
                    navigatorViewModel.navigateTo(
                        ScreenRoutes.UserDeleteScreen(
                            signedInUser!!.userId,
                            signedInUser!!.username,
                            signedInUser!!.profilePictureUrl,
                            signedInUser!!.phoneNumber,
                            signedInUser!!.email
                        )
                    )
                }
            }

            SettingAccountOptions.LogOut -> {
                showLogoutDialog = true
//                CoroutineScope(Dispatchers.Main).launch {
//                    googleAuthUiClient.signOut()
//                    signedInUser = null
//                    MixedUtils.showToast(context, R.string.logged_out_successfully)
//                }
            }
        }
    }


    if (showLogoutDialog) {
        CommonDialog(
            title = stringResource(R.string.log_out),
            message = stringResource(R.string.are_you_sure_you_want_to_log_out),
            confirmText = stringResource(R.string.log_out),
            dismissText = stringResource(R.string.txtid_cancel),
            onConfirm = {
                CoroutineScope(Dispatchers.Main).launch {
                    googleAuthUiClient.signOut()
                    signedInUser = null
                    MixedUtils.showToast(context, R.string.logged_out_successfully)
                }
                showLogoutDialog = false
            },
            onDismiss = {
                showLogoutDialog = false
            }
        )
    }

    val scrollState = rememberScrollState()

    Scaffold { innerPadding ->
        AppColumn(Modifier.fillMaxSize()) {
//            AppAppbar(
//                innerPadding = innerPadding,
//                title = stringResource(R.string.setting),
//            )
            AppColumn(
                modifier = Modifier
                    .padding(innerPadding)
                    .fillMaxSize()
                    .padding(horizontal = 16.dp)
                    .verticalScroll(scrollState),
            ) {
                AppSpacer(16.dp)
                AppRow(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Khi người dùng chưa đăng nhập
                    if (signedInUser == null) {
                        AppRow(
                            modifier = Modifier
                                .fillMaxWidth()
                                .minHeight(AppSize.MIN_TOUCH_SIZE)
                                .appClickable {
                                    coroutineScope.launch {
                                        val signInIntentSender = googleAuthUiClient.signIn()
                                        launcher.launch(
                                            IntentSenderRequest
                                                .Builder(signInIntentSender ?: return@launch)
                                                .build()
                                        )
                                    }
                                }
                                .padding(horizontal = 4.dp)
                                .padding(vertical = 4.dp),
                            contentAlignment = Alignment.CenterStart,
                        ) {
                            AppIcon(
                                R.drawable.ic_google,
                                size = 36.dp,
                                tint = null,
                            )
                            AppSpacer(8.dp)
                            AppColumn {
                                AppText(
                                    text = stringResource(R.string.sign_in),
                                    fontSize = AppFontSize.BODY1,
                                    fontWeight = FontWeight.W700,
                                    color = AppColors.current.titleText,
                                    lineHeight = 24.sp
                                )
                                AppSpacer(4.dp)
                                AppText(
                                    text = stringResource(
                                        R.string.txtid_sign_in_reward,
                                        String.format(Locale.getDefault(), "%,d", RconfAssist.getInt(RconfConst.CREDIT_SIGN_IN_REWARD))
                                    ),
                                    fontSize = AppFontSize.BODY2,
                                    fontWeight = FontWeight.W400,
                                    color = AppColors.current.titleHintText
                                )
                            }
                        }
                    } else { // Khi người dùng đăng nhập
                        val user = signedInUser ?: return@AppRow
                        AppRow(
                            modifier = Modifier
                                .fillMaxWidth()
                                .minHeight(AppSize.MIN_TOUCH_SIZE)
//                                .appClickable {
//                                    navigatorViewModel.navigateTo(
//                                        ScreenRoutes.UserInfoScreen(
//                                            user.userId,
//                                            user.username,
//                                            user.profilePictureUrl,
//                                            user.phoneNumber,
//                                            user.email
//                                        )
//                                    )
//                                }
                                .padding(horizontal = 4.dp)
                                .padding(vertical = 4.dp),
                            contentAlignment = Alignment.CenterStart,
                        ) {
                            AppIcon(
                                if (user.profilePictureUrl.isNullOrEmpty())
                                    painterResource(id = R.drawable.ic_google)
                                else
                                    rememberAsyncImagePainter(user.profilePictureUrl),
                                modifier = Modifier.clip(CircleShape),
                                size = 36.dp,
                                tint = null,
                            )
                            AppSpacer(8.dp)
                            AppColumn(modifier = Modifier.weight(1f)) {
                                AppRow() {
                                    AppText(
                                        text = "${user.username}",
                                        fontSize = AppFontSize.BODY1,
                                        fontWeight = FontWeight.W700,
                                        color = AppColors.current.titleText,
                                        lineHeight = 24.sp
                                    )
                                    AppSpacer(modifier = Modifier.weight(1f))
                                    AppRow(
                                        verticalAlignment = Alignment.CenterVertically,
                                    ) {
                                        AppGlideImage(
                                            resId = R.drawable.svg_ic_coin_2,
                                            modifier = Modifier.size(20.dp)
                                        )
                                        AppSpacer(4.dp)
                                        AppText(
                                            text = StringUtils.formatCoin(coinTotal),
                                            fontSize = AppFontSize.BODY2,
                                            fontWeight = FontWeight.W700,
                                            lineHeight = 20.sp,
                                            color = AppColors.current.text
                                        )
                                    }
                                }
                                AppSpacer(4.dp)
                                if (user.email != null) {
                                    AppText(
                                        text = "${stringResource(R.string.txtid_email)}: ${user.email}",
                                        fontSize = AppFontSize.BODY2,
                                        fontWeight = FontWeight.W400,
                                        color = AppColors.current.textHintColor
                                    )
                                }
                            }
                            AppSpacer(8.dp)
                        }
                    }
                }
                AppSpacer(16.dp)

                //
                AppRow(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            AppColors.current.backgroundContent,
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(start = 16.dp, top = 8.dp, end = 12.dp, bottom = 8.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    AppText(
                        text = stringResource(R.string.get_more_answers_with_coins),
                        fontSize = AppFontSize.BODY1,
                        fontWeight = FontWeight.W500,
                        color = AppColors.current.titleText,
                        lineHeight = 24.sp,
                        modifier = Modifier.weight(1f)
                    )
                    AppRow(
                        modifier = Modifier
                            .clickable {
                                navigatorViewModel.navigateTo(ScreenRoutes.InAppPurchaseRoute())
                            }
                            .background(
                                AppColors.current.buttonColor,
                                shape = RoundedCornerShape(8.dp)
                            )
                            .padding(vertical = 8.dp, horizontal = 12.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        AppText(
                            text = stringResource(R.string.go),
                            color = Color.White, //color
                            fontSize = AppFontSize.BODY2,
                            lineHeight = 24.sp,
                            fontWeight = FontWeight.W500
                        )
                        AppGlideImage(
                            modifier = Modifier
                                .padding(start = 4.dp)
                                .size(24.dp),
                            resId = R.drawable.svg_ic_coin_2,
                        )
                    }
                }

                //System
                AppSpacer(16.dp)
                AppText(
                    modifier = Modifier
                        .padding(start = 16.dp)
                        .conditional(CommFigs.IS_ADD_TEST_DEVICE) {
                            appClickable {
                                navigatorViewModel.navigateTo(ScreenRoutes.ScreenTestKey())
                            }
                        },
                    text = stringResource(R.string.txtid_system).uppercase(),
                    fontSize = AppFontSize.BODY2,
                    fontWeight = FontWeight.W400,
                    color = AppColors.current.titleContent,
                    lineHeight = 20.sp,
                )
                AppSpacer(4.dp)

                AppColumnCentered(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            AppColors.current.backgroundContent,
                            shape = RoundedCornerShape(8.dp)
                        ),
                ) {
                    filteredSettingSystemOptions
                        .forEachIndexed { idx, settingIt ->
                            val hint = when (settingIt) {
                                SettingSystemOptions.Theme -> when (currentAppTheme) {
                                    AppThemeMode.DARK -> context.getString(R.string.txtid_dark)
                                    AppThemeMode.LIGHT -> context.getString(R.string.txtid_light)
                                    AppThemeMode.SYSTEM, null -> context.getString(R.string.txtid_system)
                                }

                                SettingSystemOptions.AIAssistance
                                    -> when (PrefAssist.getString(PrefConst.MODEL_AI)) {
                                    ModelAiMode.GEMINI.name -> nameGeminiAi
                                    ModelAiMode.GPT.name -> nameGptAi
                                    else -> nameGptAi
                                }

                                else -> null
                            }

                            val title = stringResource(settingIt.stringsId)
                            AppRowContent(
                                modifier = Modifier
                                    .clip(
                                        RoundedCornerShape(
                                            topStart = 8.dp,
                                            topEnd = 8.dp,
                                            bottomStart = 8.dp,
                                            bottomEnd = 8.dp
                                        )
                                    ),
                                title = title,
                                icon = settingIt.icon,
                                hint = hint,
                                showRight = true
                            ) {
                                funcOnClickSettingSystemItem(settingIt)
                            }
                            if (settingIt != filteredSettingSystemOptions.last())
                                AppRow(modifier = Modifier.fillMaxWidth()) {
                                    AppSpacer(width = 56.dp)
                                    AppDivider(
                                        color = AppColors.current.divider1
                                    )
                                }
                        }
                }

                //Developer
                AppSpacer(16.dp)
                AppText(
                    modifier = Modifier.padding(start = 16.dp),
                    text = stringResource(R.string.developer).uppercase(),
                    fontSize = AppFontSize.BODY2,
                    fontWeight = FontWeight.W400,
                    color = AppColors.current.titleContent,
                    lineHeight = 20.sp,
                )
                AppSpacer(4.dp)
                AppColumnCentered(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            AppColors.current.backgroundContent,
                            shape = RoundedCornerShape(8.dp)
                        ),
                ) {
                    SettingDeveloperOptions.entries
                        .forEachIndexed { idx, settingIt ->
//                            if (settingIt == SettingDeveloperOptions.Feedback) return@forEachIndexed
                            val hint = null
                            val title = if (settingIt == SettingDeveloperOptions.Version) {
                                stringResource(settingIt.stringsId, BuildConfig.VERSION_NAME)
                            } else {
                                stringResource(settingIt.stringsId)
                            }
                            AppRowContent(
                                modifier = Modifier
                                    .clip(
                                        RoundedCornerShape(
                                            topStart = 8.dp,
                                            topEnd = 8.dp,
                                            bottomStart = 8.dp,
                                            bottomEnd = 8.dp
                                        )
                                    ),
                                title = title,
                                icon = settingIt.icon,
                                hint = hint,
                                showRight = settingIt != SettingDeveloperOptions.Version
                            ) {
                                funcOnClickSettingDeveloperItem(settingIt)
                            }
                            if (settingIt != SettingDeveloperOptions.entries.last())
                                AppRow(modifier = Modifier.fillMaxWidth()) {
                                    AppSpacer(width = 56.dp)
                                    AppDivider(
                                        color = AppColors.current.divider1
                                    )
                                }
                        }
                }

                //account
                if (signedInUser != null) {
                    AppSpacer(16.dp)
                    AppText(
                        modifier = Modifier.padding(start = 16.dp),
                        text = stringResource(R.string.account).uppercase(),
                        fontSize = AppFontSize.BODY2,
                        fontWeight = FontWeight.W400,
                        color = AppColors.current.titleContent,
                        lineHeight = 20.sp,
                    )
                    AppSpacer(4.dp)
                    AppColumnCentered(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                AppColors.current.backgroundContent,
                                shape = RoundedCornerShape(8.dp)
                            ),
                    ) {
                        SettingAccountOptions.entries
                            .forEachIndexed { idx, settingIt ->
                                val hint = null
                                val title = stringResource(settingIt.stringsId)
                                AppRowContent(
                                    modifier = Modifier
                                        .clip(
                                            RoundedCornerShape(
                                                topStart = 8.dp,
                                                topEnd = 8.dp,
                                                bottomStart = 8.dp,
                                                bottomEnd = 8.dp
                                            )
                                        ),
                                    title = title,
                                    titleColor = if (settingIt == SettingAccountOptions.LogOut)
                                        AppColors.current.textError
                                    else
                                        null,
                                    icon = settingIt.icon,
                                    hint = hint,
                                    showRight = settingIt != SettingAccountOptions.LogOut
                                ) {
                                    funcOnClickSettingAccountItem(settingIt)
                                }
                                if (settingIt != SettingAccountOptions.entries.last())
                                    AppRow(modifier = Modifier.fillMaxWidth()) {
                                        AppSpacer(width = 56.dp)
                                        AppDivider(
                                            color = AppColors.current.divider1
                                        )
                                    }
                            }
                    }
                }
                AppSpacer(Modifier.weight(1f))
                AppSpacer(16.dp)
                AppSpacer(innerPaddingHome.calculateBottomPadding())
            }
        }
        if (isLoading) {
            AppBoxCentered(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f))
                    .clickable(enabled = false) {},
            ) {
                CircularProgressIndicator()
            }
        }
    }
    ModelAiModeBottomSheet(
        showIt = showBottomSheetModelAiMode,
        onDismissRequest = { showBottomSheetModelAiMode = false }
    )
}

@Composable
fun AppRowContent(
    modifier: Modifier = Modifier,
    icon: Int,
    title: String,
    titleColor: Color? = null,
    hint: String?,
    showRight: Boolean = true,
    onClick: () -> Unit = {},
) {
    AppRow(
        modifier = modifier
            .fillMaxWidth()
            .appClickable { onClick() }
            .background(AppColors.current.backgroundContent)
            .padding(start = 16.dp, top = 12.dp, end = 12.dp, bottom = 12.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        AppRow(
            modifier = Modifier.weight(1f),
            verticalAlignment = Alignment.CenterVertically
        ) {
            AppGlideImage(
                modifier = Modifier
                    .size(32.dp),
                resId = icon,
            )
            AppSpacer(12.dp)
            AppText(
                text = title,
                fontSize = AppFontSize.BODY1,
                fontWeight = FontWeight.W500,
                color = titleColor ?: AppColors.current.titleText,
                lineHeight = 24.sp
            )
        }

        if (showRight) {
            AppRow(
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (hint != null) {
                    AppText(
                        text = hint,
                        fontSize = AppFontSize.BODY2,
                        fontWeight = FontWeight.W400,
                        color = AppColors.current.textHintColor,
                        lineHeight = 20.sp
                    )
                    AppSpacer(8.dp)
                }
                AppSpacer(8.dp)
                AppGlideImage(
                    modifier = Modifier
                        .size(24.dp),
                    resId = R.drawable.svg_ic_left_arrow_new,
                )
            }
        }
    }
}

