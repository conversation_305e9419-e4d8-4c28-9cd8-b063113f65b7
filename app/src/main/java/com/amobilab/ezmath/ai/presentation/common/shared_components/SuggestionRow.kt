package com.amobilab.ezmath.ai.presentation.common.shared_components

import amobi.module.compose.foundation.AppBox
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppTextAutoSize
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.amobilab.ezmath.ai.data.models.PromptSuggestion

@Composable
fun SuggestionRow(
    suggestions: List<PromptSuggestion>,
    onSuggestionClick: (String) -> Unit,
    isFeature: Boolean = false
) {
    LazyRow(
        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(suggestions) { suggestion ->
            val promptText = stringResource(id = if(!isFeature)suggestion.prompt else suggestion.title)
            AppBox(
                modifier = Modifier
                    .width(200.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(AppColors.current.backgroundContent)
                    .clickable { onSuggestionClick(promptText) }
                    .padding(12.dp)
            ) {
                AppColumn {
                    AppTextAutoSize(
                        text = stringResource(suggestion.title),
                        color = AppColors.current.text,
                        fontWeight = FontWeight.Bold,
                        fontSize = AppFontSize.SMALL,
                        lineHeight = 16.sp,
                        maxLines = 1
                    )
                    AppSpacer(6.dp)
                    AppTextAutoSize(
                        text = stringResource(suggestion.prompt),
                        color = AppColors.current.titleHintText,
                        fontSize = AppFontSize.SMALL,
                        lineHeight = 16.sp,
                        maxLines = 2
                    )
                }
            }
        }
    }
}
