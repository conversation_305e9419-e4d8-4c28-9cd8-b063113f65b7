<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="generate_outline" translate_by="gpt">Taslak Oluştur</string>
    <string name="placeholder_default" translate_by="gpt">Metninizi buraya girin…</string>
    <string name="essay_screen_description" translate_by="gemini">Elbette! Bu konuda yardımcı olabilirim. Lütfen aşağıdaki makaleniz için bazı detaylar sağlayın.</string>
    <string name="label_choose_topic" translate_by="gpt">Bir konu seçin</string>
    <string name="label_essay_type" translate_by="gemini"><PERSON><PERSON><PERSON></string>
    <string name="label_word_count" translate_by="gpt">Kelime sayısı</string>
    <string name="label_language_tone" translate_by="gpt">Dil + ton</string>
    <string name="placeholder_topic" translate_by="gemini"><PERSON><PERSON>r bulduğunuz bir yeri tarif edin</string>
    <string name="placeholder_essay_type" translate_by="gpt">Örnek: <PERSON><PERSON>, <PERSON><PERSON><PERSON>…</string>
    <string name="placeholder_word_count" translate_by="gemini">Örn: 300 kelime, 500 kelime, 1000 kelime…</string>
    <string name="placeholder_language_tone" translate_by="google">Örn: resmi, akademik, …</string>
    <string name="research_screen_description" translate_by="google">Araştırma ve analiz yoluyla ham verileri anlamlı, görsel hikayelere dönüştürmek için bir yer.</string>
    <string name="label_research_topic" translate_by="gpt">Araştırma konusu</string>
    <string name="label_research_goal" translate_by="gemini">Araştırma hedefi</string>
    <string name="label_preferred_sources" translate_by="gpt">Tercih edilen kaynaklar</string>
    <string name="label_depth_length" translate_by="gpt">Derinlik / Uzunluk</string>
    <string name="label_academic_level" translate_by="gpt">Akademik seviye</string>
    <string name="placeholder_research_topic" translate_by="gpt">İklim değişikliği, Yapay Zekanın İşler Üzerindeki Etkisi, …</string>
    <string name="placeholder_research_goal" translate_by="gpt">Bilgi Toplama, Trend Analizi…</string>
    <string name="placeholder_preferred_sources" translate_by="gpt">Bilimsel dergiler, kitaplar, resmi makaleler</string>
    <string name="placeholder_depth_length" translate_by="gemini">Örn: 300 kelime, 500 kelime, 1000 kelime…</string>
    <string name="placeholder_academic_level" translate_by="gpt">Lise öğrencileri, üniversite öğrencileri, ileri düzey araştırma, …</string>
    <string name="literature_screen_description" translate_by="gemini">Kelimelerden gizli anlamlara, her edebi eserin gerçek değerini ortaya çıkarmanıza yardımcı oluyoruz.</string>
    <string name="label_title_of_work" translate_by="gemini">Çalışmanın Başlığı</string>
    <string name="label_author" translate_by="gpt">Yazar</string>
    <string name="label_analysis_type" translate_by="gpt">Ne analiz etmek istiyorsunuz?</string>
    <string name="label_format" translate_by="gemini">Uzunluk / biçim</string>
    <string name="placeholder_title" translate_by="gemini">Muhteşem Gatsby</string>
    <string name="placeholder_author" translate_by="gpt">F. Scott Fitzgerald</string>
    <string name="placeholder_analysis_type" translate_by="gpt">Karakter analizi, Ana temalar…</string>
    <string name="placeholder_format" translate_by="gemini">Örn: 300 kelime, 500 kelime, 1000 kelime…</string>
    <string name="placeholder_academic_level_literature" translate_by="gpt">Ortaokul, lise veya üniversite, …</string>
    <string name="research_outline_topic_label" translate_by="gpt">📘 Araştırma Konusu: %1$s</string>
    <string name="research_outline_goal_label" translate_by="gpt">🎯 Araştırma Hedefi: %1$s</string>
    <string name="research_outline_sources_label" translate_by="gpt">📚 Tercih Edilen Kaynaklar: %1$s</string>
    <string name="research_outline_depth_label" translate_by="gpt">📏 Derinlik/Uzunluk: %1$s</string>
    <string name="research_outline_academic_level_label" translate_by="gpt">🎓 Akademik Seviye: %1$s</string>
    <string name="research_outline_suggested_outline_label" translate_by="gpt">🧾 Önerilen Taslak:</string>
    <string name="research_outline_introduction_title" translate_by="gpt">1. Giriş</string>
    <string name="research_outline_introduction_overview" translate_by="gpt">- %1$s hakkında kısa bir genel bakış</string>
    <string name="research_outline_introduction_importance" translate_by="gemini">%1$s seviyesinde araştırmanın önemi</string>
    <string name="research_outline_objectives_title" translate_by="gpt">2. Hedefler</string>
    <string name="research_outline_objectives_goal" translate_by="gpt">- Ana hedefi netleştir: %1$s</string>
    <string name="research_outline_methodology_title" translate_by="gpt">3. Metodoloji</string>
    <string name="research_outline_methodology_approach" translate_by="gpt">- Araştırma yaklaşımı</string>
    <string name="research_outline_methodology_sources" translate_by="gpt">- Veri kaynakları: %1$s</string>
    <string name="research_outline_key_insights_title" translate_by="gemini">4. Temel Bilgiler</string>
    <string name="research_outline_key_insights_trends" translate_by="gemini">Trendleri, gerçekleri veya analiz bulgularını tartışın</string>
    <string name="research_outline_key_insights_citations" translate_by="gpt">- Gerekirse alıntı yapın</string>
    <string name="research_outline_conclusion_title" translate_by="gpt">5. Sonuç</string>
    <string name="research_outline_conclusion_summary" translate_by="gpt">- Bulguların Özeti</string>
    <string name="research_outline_conclusion_implications" translate_by="gpt">- Sonuçlar veya gelecekteki çalışmalar</string>
    <string name="essay_outline_topic_label" translate_by="gemini">✏️ Deneme Konusu: %1$s</string>
    <string name="essay_outline_type_label" translate_by="gemini">📝 Deneme Türü: %1$s</string>
    <string name="essay_outline_word_count_label" translate_by="gpt">🔢 Kelime Sayısı: %1$s</string>
    <string name="essay_outline_language_tone_label" translate_by="gpt">🗣️ Dil ve Ton: %1$s</string>
    <string name="essay_outline_suggested_outline_label" translate_by="gpt">🧾 Önerilen Taslak:</string>
    <string name="essay_outline_introduction_title" translate_by="gpt">1. Giriş</string>
    <string name="essay_outline_introduction_topic" translate_by="gemini">Konuyu tanıt: %1$s</string>
    <string name="essay_outline_introduction_background" translate_by="gpt">- Arka plan/bağlam sağlayın</string>
    <string name="essay_outline_introduction_thesis" translate_by="gpt">Tezi belirtin</string>
    <string name="essay_outline_body_title" translate_by="gpt">2. Gövde Paragrafları</string>
    <string name="essay_outline_body_paragraph1" translate_by="gemini">Paragraf 1: İlk argüman veya nokta</string>
    <string name="essay_outline_body_paragraph2" translate_by="gpt">- Paragraf 2: Destekleyici kanıt veya anlatı</string>
    <string name="essay_outline_body_paragraph3" translate_by="gpt">- Paragraf 3: Karşı argüman veya ek detay</string>
    <string name="essay_outline_conclusion_title" translate_by="gpt">3. Sonuç</string>
    <string name="essay_outline_conclusion_summary" translate_by="gemini">Önemli noktaları özetle</string>
    <string name="essay_outline_conclusion_restate" translate_by="gpt">Tezi yeni bir şekilde yeniden ifade et.</string>
    <string name="essay_outline_conclusion_final" translate_by="gemini">Güçlü bir son düşünceyle bitir</string>
    <string name="essay_outline_notes_title" translate_by="gpt">✨ Notlar:</string>
    <string name="essay_outline_notes_tone" translate_by="gemini">%1$s tonunu koruyun</string>
    <string name="essay_outline_notes_wordcount" translate_by="gpt">- Yaklaşık %1$s toplam hedefleyin</string>
    <string name="essay_outline_notes_structure" translate_by="gpt">- Tipik %1$s makale yapısını takip edin</string>
    <string name="literature_outline_title_label" translate_by="gpt">Başlık: %1$s</string>
    <string name="literature_outline_author_label" translate_by="gpt">Yazar: %1$s</string>
    <string name="literature_outline_focus_label" translate_by="gpt">Odak: %1$s</string>
    <string name="literature_outline_length_label" translate_by="gpt">Uzunluk: %1$s</string>
    <string name="literature_outline_academic_level_label" translate_by="gpt">Akademik Seviye: %1$s</string>
    <string name="literature_outline_outline_label" translate_by="gpt">Taslak:</string>
    <string name="literature_outline_introduction_title" translate_by="gpt">1. Giriş</string>
    <string name="literature_outline_introduction_context" translate_by="gpt">Edebi eseri ve bağlamını tanıtın.</string>
    <string name="literature_outline_introduction_author" translate_by="gpt">Yazarın adını ve seçilen analiz odaklarıyla olan ilişkisini belirtin.</string>
    <string name="literature_outline_background_title" translate_by="gpt">2. Arka Plan</string>
    <string name="literature_outline_background_summary" translate_by="gemini">Konu özeti veya ana karakterler (analiz türüne bağlı olarak).</string>
    <string name="literature_outline_background_context" translate_by="gemini">Daha derin analiz için gerekli bağlamı sağlayın.</string>
    <string name="literature_outline_analysis_title" translate_by="gpt">3. Ana Analiz</string>
    <string name="literature_outline_analysis_deep_dive" translate_by="gpt">Derinlemesine incele: %1$s.</string>
    <string name="literature_outline_analysis_evidence" translate_by="gemini">Metinden kanıt kullanın: alıntılar, olaylar, sembolizm vb.</string>
    <string name="literature_outline_connections_title" translate_by="gpt">4. Bağlantılar</string>
    <string name="literature_outline_connections_themes" translate_by="gemini">Bağlantı analizini daha büyük temalara veya gerçek dünya etkilerine bağlayın.</string>
    <string name="literature_outline_connections_contrast" translate_by="gpt">İsteğe bağlı olarak diğer karakterler veya eserlerle karşılaştırın.</string>
    <string name="literature_outline_conclusion_title" translate_by="gpt">5. Sonuç</string>
    <string name="literature_outline_conclusion_insights" translate_by="gpt">Anahtar içgörüleri yeniden ifade et.</string>
    <string name="literature_outline_conclusion_value" translate_by="gemini">Çalışmanın değerini akademik bir bakış açısıyla değerlendirin.</string>
</resources>