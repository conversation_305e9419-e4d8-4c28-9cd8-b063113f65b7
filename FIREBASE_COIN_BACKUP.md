# Hệ thống Backup Coin với Firebase

## Tổng quan

Hệ thống này cung cấp một giải pháp backup đơn giản cho coin management, sử dụng Firebase Firestore như một backup khi PowerSync gặp lỗi hoặc mất kết nối.

## Cách hoạt động

### 1. Automatic Backup
- Mỗi khi cập nhật coin thông qua PowerSync, hệ thống tự động backup lên Firebase
- Nếu PowerSync fail, hệ thống sẽ fallback sang Firebase
- Sử dụng FirestoreUtils hiện có để tương thích với code cũ

### 2. Fallback Mechanism
```kotlin
// Trong UserRepository.updateCoin()
try {
    // Cập nhật PowerSync trước
    database.writeTransaction { ... }
    
    // Backup lên Firebase sau khi thành công
    backupCoinToFirebase(coin)
    
} catch (e: PowerSyncException) {
    // Nếu PowerSync fail, backup lên Firebase
    backupCoinToFirebase(coin)
    // Cập nhật local để UI không bị mất sync
    PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE, coin)
}
```

### 3. Recovery từ Firebase
```kotlin
// Trong UserRepository.getCoin()
try {
    // Thử lấy từ PowerSync trước
    return powerSyncCoin
} catch (e: Exception) {
    // Fallback: lấy từ Firebase/local
    return localCoin
}
```

## UI Components

### CoinSyncButton
- **Vị trí**: Settings tab (chỉ hiển thị khi user đã đăng nhập)
- **Chức năng**: Cho phép user manual sync với Firebase
- **Trạng thái**: Loading, Success, Error

### Cách sử dụng
```kotlin
@Composable
fun YourScreen() {
    CoinSyncButton(
        coinViewModel = coinViewModel
    )
}
```

## Methods mới trong CoinViewModel

### 1. performManualSync()
- Thực hiện sync thủ công với Firebase
- Gọi UserRepository.syncCoinFromFirebase()
- Cập nhật UI với coin mới

### 2. checkAndRestoreCoinFromFirebase()
- Kiểm tra nếu coin = 0 thì thử restore từ Firebase
- Tự động gọi khi cần thiết

## Methods mới trong UserRepository

### 1. backupCoinToFirebase(coin: Long)
- Backup coin lên Firebase sử dụng FirestoreUtils
- Private method, tự động gọi trong updateCoin()

### 2. syncCoinFromFirebase()
- Sync coin từ Firebase về local
- Thử cập nhật lại PowerSync nếu có thể
- Public method cho manual sync

## Tích hợp với code hiện tại

### 1. Không breaking changes
- Sử dụng FirestoreUtils hiện có
- Không thay đổi API của CoinViewModel
- Tương thích với logic coin hiện tại

### 2. Error handling
- PowerSync fail → Firebase backup
- Firebase fail → Local storage
- UI luôn được cập nhật

### 3. User experience
- Transparent backup (user không cần biết)
- Manual sync option trong Settings
- Visual feedback khi sync

## Testing

### 1. Test PowerSync failure
```kotlin
// Ngắt kết nối PowerSync
AppDatabase.getInstance().disconnectAndClear()

// Thử cập nhật coin
coinViewModel.updateCoinBalance(100, "test")

// Kiểm tra coin có được backup lên Firebase không
```

### 2. Test Firebase fallback
```kotlin
// Ngắt internet
// Kiểm tra app có hoạt động với local storage không
```

## Monitoring

### Debug logs
- "PowerSync updateCoin thành công/thất bại"
- "Firebase backup thành công/thất bại"
- "Sync từ Firebase thành công"
- "Manual sync thành công/thất bại"

### User feedback
- Loading state trong CoinSyncButton
- Success message sau khi sync
- Error handling transparent

## Best practices

### 1. Performance
- Backup chỉ khi cần thiết
- Sử dụng existing FirestoreUtils
- Không block UI thread

### 2. Data consistency
- PowerSync là primary source
- Firebase là backup
- Local storage là fallback cuối cùng

### 3. User experience
- Automatic backup transparent
- Manual sync option available
- Clear visual feedback

## Troubleshooting

### Common issues

1. **Coin không sync**
   - Kiểm tra Firebase connection
   - Kiểm tra user authentication
   - Xem debug logs

2. **Manual sync không hoạt động**
   - Kiểm tra user đã đăng nhập chưa
   - Kiểm tra FirestoreUtils.syncCoinsFirestore()

3. **PowerSync và Firebase không consistent**
   - Chạy manual sync
   - Kiểm tra conflict resolution logic

### Debug commands
```kotlin
// Manual sync
coinViewModel.performManualSync()

// Check và restore
coinViewModel.checkAndRestoreCoinFromFirebase()

// Direct Firebase sync
userRepository.syncCoinFromFirebase()
```

## Future improvements
- Real-time sync với Firebase listeners
- Conflict resolution UI
- Sync status indicator
- Offline queue management
