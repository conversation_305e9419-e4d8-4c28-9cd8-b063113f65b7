package amobi.module.gpdr

import amobi.module.common.advertisements.AdvertsConfig
import amobi.module.common.configs.CommFigs
import amobi.module.common.utils.DebugLogCustom
import amobi.module.common.views.CommActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.google.android.ump.ConsentDebugSettings
import com.google.android.ump.ConsentRequestParameters
import com.google.android.ump.FormError
import com.google.android.ump.UserMessagingPlatform
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean

class GDPRConsent(
    activity: CommActivity,
    private val onConsentFinished: ((Boolean) -> Unit?)? = null,
) {
    private val isGDPRInitSuccess = AtomicBoolean(false)
    private val isMobileAdsInitializeCalled = AtomicBoolean(false)

    init {
        if (AdvertsConfig.instance.isHideAd) {
            onConsentFinished?.invoke(false)
        } else {
            val consentInformation = UserMessagingPlatform.getConsentInformation(activity)

            // Set tag for under age of consent. false means users are not under age
            // of consent.
            val params: ConsentRequestParameters =
                if (CommFigs.IS_ADD_TEST_DEVICE) {
//                consentInformation.reset()
                    val debugSettings: ConsentDebugSettings =
                        ConsentDebugSettings
                            .Builder(activity)
//                        .setDebugGeography(ConsentDebugSettings.DebugGeography.DEBUG_GEOGRAPHY_EEA)
//                        .addTestDeviceHashedId(AdvertsConfig.instance.getAdMobDeviceAndroidId(activity))
                            .build()
                    ConsentRequestParameters
                        .Builder()
                        .setConsentDebugSettings(debugSettings)
                        .setTagForUnderAgeOfConsent(false)
                        .build()
                } else {
                    ConsentRequestParameters
                        .Builder()
                        .setTagForUnderAgeOfConsent(false)
                        .build()
                }

            val gpdrIsEnabled = GDPRAssist.isGDPR()
            val gpdrCanShowAds = GDPRAssist.canShowAds()
            if (gpdrIsEnabled && !gpdrCanShowAds) {
                DebugLogCustom.logd("GDPRConsent: reset $gpdrIsEnabled $gpdrCanShowAds")
                consentInformation.reset()
            } else {
                DebugLogCustom.logd("GDPRConsent: not reset $gpdrIsEnabled $gpdrCanShowAds")
            }

            activity.lifecycleScope.launch {
                activity.repeatOnLifecycle(Lifecycle.State.RESUMED) {
                    delay(15 * CommFigs.MILLIS_SECOND)
                    if (!isGDPRInitSuccess.get()) {
                        initializeMobileAdsSdk(true)
                    }
                }
            }

            consentInformation.requestConsentInfoUpdate(
                activity,
                params,
                {
                    isGDPRInitSuccess.set(true)

                    UserMessagingPlatform.loadAndShowConsentFormIfRequired(activity) { loadAndShowError: FormError? ->
                        if (loadAndShowError != null) {
                            // Consent gathering failed.
                            DebugLogCustom.logd("${loadAndShowError.errorCode}: ${loadAndShowError.message}")
                        }

                        // Consent has been gathered.
//                    if (consentInformation.canRequestAds()) {
//                        initializeMobileAdsSdk()
//                    }
                        initializeMobileAdsSdk(false)
                    }
                },
                { requestConsentError: FormError ->
                    // Consent gathering failed.
                    DebugLogCustom.logd("${requestConsentError.errorCode}: ${requestConsentError.message}")
                    initializeMobileAdsSdk(false)
                },
            )
            // Check if you can initialize the Google Mobile Ads SDK in parallel
            // while checking for new consent information. Consent obtained in
            // the previous session can be used to request ads.
            if (consentInformation.canRequestAds()) {
                initializeMobileAdsSdk(false)
            }
        }
    }

    private fun initializeMobileAdsSdk(isTimeOutGDPR: Boolean) {
        if (isMobileAdsInitializeCalled.getAndSet(true)) {
            return
        }

        onConsentFinished?.invoke(isTimeOutGDPR)
    }
}
