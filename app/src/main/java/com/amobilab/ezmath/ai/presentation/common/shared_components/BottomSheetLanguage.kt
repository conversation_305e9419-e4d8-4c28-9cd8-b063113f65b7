package com.amobilab.ezmath.ai.presentation.common.shared_components


import amobi.module.common.configs.PrefAssist
import amobi.module.compose.extentions.appClickable
import amobi.module.compose.foundation.AppBox
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppDivider
import amobi.module.compose.foundation.AppIcon
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontFamily
import amobi.module.compose.theme.AppFontSize
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.Icon
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.pref.PrefConst
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun BottomSheetLanguage(onDismiss: (newLanguage: String?) -> Unit) {
    val modalBottomSheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true,
    )
    var searchQuery by remember { mutableStateOf(TextFieldValue("")) }
    var recentLanguages by remember {
        mutableStateOf(listOf("en", "vi")
        )
    }
    val focusRequester = remember { FocusRequester() }

    val coroutineScope = rememberCoroutineScope()
    ModalBottomSheet(
        modifier = Modifier.padding(top = 50.dp),
        onDismissRequest = {
            onDismiss(null)
            },
        sheetState = modalBottomSheetState,
        containerColor = AppColors.current.bottomSheetBackground,
        dragHandle = null
    ) {
        Spacer(modifier = Modifier.height(16.dp))

        // Header
        SelectLanguageHeader(){
            coroutineScope.launch {
                modalBottomSheetState.hide()
                delay(300)
                onDismiss(PrefAssist.getString(PrefConst.TRANSLATE_TARGET))
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        AppRow(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp)
                .padding(horizontal = 12.dp)
                .background(AppColors.current.focusedContainerColor , shape = RoundedCornerShape(8.dp)),
            verticalAlignment = Alignment.CenterVertically
        ) {
            AppRow(
                modifier = Modifier
                    .weight(1f),
                verticalAlignment = Alignment.CenterVertically

            ) {
                AppIcon(
                    R.drawable.ic_search_new,
                    size = 16.dp,
                    tint = AppColors.current.textHintColor,
                ) { focusRequester.requestFocus() }

                val interactionSource = remember { MutableInteractionSource() }
                BasicTextField(
                    modifier = Modifier
                        .weight(1f)
                        .focusRequester(focusRequester),
                    value = searchQuery,
                    onValueChange = { searchQuery = it },
                    visualTransformation = VisualTransformation.None,
                    interactionSource = interactionSource,
                    textStyle = TextStyle(
                        color = AppColors.current.text,
                        fontSize = AppFontSize.BODY1,
                        fontFamily = AppFontFamily.get(),
                        fontWeight = FontWeight.Medium,
                    ),
                    cursorBrush = SolidColor(AppColors.current.text),
                    enabled = true,
                    singleLine = true,
                ) { innerTextField ->
                    TextFieldDefaults.DecorationBox(
                        value = searchQuery.text,
                        visualTransformation = VisualTransformation.None,
                        innerTextField = innerTextField,
                        singleLine = true,
                        enabled = true,
                        interactionSource = interactionSource,
                        contentPadding = PaddingValues(vertical = 12.dp),
                        shape = RoundedCornerShape(12.dp),
                        colors = TextFieldDefaults.colors(
                            focusedIndicatorColor = Color.Transparent,
                            unfocusedIndicatorColor = Color.Transparent,
                            disabledIndicatorColor = Color.Transparent,
                            focusedContainerColor = Color.Transparent,
                            unfocusedContainerColor = Color.Transparent,
                        ),
                        placeholder = {
                            AppText(
                                text = stringResource(R.string.search_languages).replace(
                                    "...",
                                    "…"
                                ),
                                color = AppColors.current.textHintColor,
                                fontSize = AppFontSize.BODY2,
                            )
                        }
                    )
                }
            }
        }
        AppSpacer(8.dp)
        // Country List
        CountryList(
            searchQuery = searchQuery.text,
            recentLanguageCodes = recentLanguages,
            selectedLanguageCode = PrefAssist.getString(PrefConst.TRANSLATE_TARGET ,"English (US)"),
            onSelectLanguage = { newLanguageCode ->
                // update recent
                recentLanguages = listOf(newLanguageCode) + recentLanguages.filter { it != newLanguageCode }.take(2)
                PrefAssist.setString(PrefConst.TRANSLATE_TARGET, newLanguageCode)
                coroutineScope.launch {
                    modalBottomSheetState.hide()
                    delay(300)
                    onDismiss(newLanguageCode)
                }
            }
        )

        AppSpacer(20.dp)
    }
}

@Composable
private fun CountryList(
    searchQuery: String,
    selectedLanguageCode: String?,
    recentLanguageCodes: List<String>,
    onSelectLanguage: (String) -> Unit
) {
    val filteredLanguages = if (searchQuery.isBlank()) languages
    else languages.filter {
        it.englishName.contains(searchQuery, ignoreCase = true)
                || it.localizedName?.contains(searchQuery, ignoreCase = true) == true
    }

    val recentLanguages = recentLanguageCodes.mapNotNull { code ->
        languages.find { it.code == code }
    }

    LazyColumn {
        if (recentLanguages.isNotEmpty()) {
            item {
                AppSpacer(4.dp)
                AppText(
                    text = stringResource(R.string.recent_languages).uppercase(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 4.dp)
                        .padding(start = 12.dp),
                    fontSize = AppFontSize.BODY2,
                    lineHeight = 20.sp,
                    fontWeight = FontWeight.W400,
                    color = AppColors.current.titleContent
                )

                // Box chứa danh sách Recent
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 12.dp, vertical = 4.dp)
                        .clip(RoundedCornerShape(12.dp))
                        .background(AppColors.current.backgroundContent)
                        .padding(12.dp)
                ) {
                    AppColumn {
                        recentLanguages.forEachIndexed { index, language ->
                            LanguageRow(language,selectedLanguageCode, onSelectLanguage)
                            if (index < recentLanguages.lastIndex) {
                                AppSpacer(8.dp)
                                AppDivider()
                                AppSpacer(8.dp)
                            }
                        }
                    }
                }
            }
        }

        item {
            AppSpacer(4.dp)
            AppText(
                text = stringResource(R.string.all_languages).uppercase(),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 4.dp)
                    .padding(start = 12.dp),
                fontSize = AppFontSize.BODY2,
                lineHeight = 20.sp,
                fontWeight = FontWeight.W400,
                color = AppColors.current.titleContent
            )

            // Box chứa danh sách All
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 12.dp , vertical = 4.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(AppColors.current.backgroundContent)
                    .padding(12.dp)
            ) {
                AppColumn {
                    filteredLanguages.forEachIndexed { index, language ->
                        LanguageRow(language, selectedLanguageCode , onSelectLanguage)
                        if (index < filteredLanguages.lastIndex) {
                            AppSpacer(8.dp)
                            AppDivider()
                            AppSpacer(8.dp)
                        }
                    }
                }
            }
        }
    }
}


@Composable
private fun LanguageRow(
    language: LanguageData,
    selectedLanguageCode: String?,
    onSelectLanguage: (String) -> Unit
) {
    AppRow(
        modifier = Modifier
            .fillMaxWidth()
            .appClickable { onSelectLanguage(language.englishName) }
            .padding(horizontal = 12.dp, vertical = 4.dp)
    ) {
        // Emoji (tuỳ chọn nếu muốn)
        language.emojiSymbol?.let {
            AppText(
                text = it,
                modifier = Modifier.padding(end = 12.dp)
            )
        }

        AppText(
            modifier = Modifier.weight(1f),
            text = buildString {
                append(language.englishName)
                language.localizedName?.let {
                    append(" [")
                    append(it)
                    append("]")
                }
            },
            color = AppColors.current.text
        )

        // Dấu tick nếu ngôn ngữ được chọn
        if (language.englishName == selectedLanguageCode) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "Selected",
                tint = AppColors.current.checkColor
            )
        }
    }
}


private data class LanguageData(
    val code: String,
    val englishName: String,
    val localizedName: String? = null,
    val emojiSymbol: String? = null
)

private val languages = listOf(
    LanguageData("en", "English (US)", null, "\uD83C\uDDFA\uD83C\uDDF8"),
    LanguageData("zh-cn", "Simplified Chinese", "简体中文", "\uD83C\uDDE8\uD83C\uDDF3"),
    LanguageData("hi", "Hindi", "हिन्दी", "\uD83C\uDDEE\uD83C\uDDF3"),
    LanguageData("es", "Spanish", "Español", "\uD83C\uDDEA\uD83C\uDDF8"),
    LanguageData("fr", "French", "Français", "\uD83C\uDDEB\uD83C\uDDF7"),
    LanguageData("ar", "Arabic", "العربية", "\uD83C\uDDE6\uD83C\uDDEA"),
    LanguageData("bn", "Bengali", "বাংলা", "\uD83C\uDDE7\uD83C\uDDE9"),
    LanguageData("ru", "Russian", "Русский", "\uD83C\uDDF7\uD83C\uDDFA"),
    LanguageData("pt", "Portuguese", "Português", "\uD83C\uDDF5\uD83C\uDDF9"),
    LanguageData("in", "Indonesian", "Bahasa Indonesia", "\uD83C\uDDEE\uD83C\uDDE9"),
    LanguageData("de", "German", "Deutsch", "\uD83C\uDDE9\uD83C\uDDEA"),
    LanguageData("ja", "Japanese", "日本語", "\uD83C\uDDEF\uD83C\uDDF5"),
    LanguageData("te", "Telugu", "తెలుగు", "\uD83C\uDDEE\uD83C\uDDF3"),
    LanguageData("tr", "Turkish", "Türkçe", "\uD83C\uDDF9\uD83C\uDDF7"),
    LanguageData("zh-tw", "Traditional Chinese", "繁體中文", "\uD83C\uDDF9\uD83C\uDDFC"),
    LanguageData("vi", "Vietnamese", "Tiếng Việt", "\uD83C\uDDFB\uD83C\uDDF3"),
    LanguageData("af", "Afrikaans", "Afrikaans", "\uD83C\uDDE6\uD83C\uDDF8"),
    LanguageData("az", "Azerbaijani", "Azərbaycanca", "\uD83C\uDDE6\uD83C\uDDF8"),
    LanguageData("be", "Belarusian", "беларуская", "\uD83C\uDDE7\uD83C\uDDE9"),
    LanguageData("bg", "Bulgarian", "български", "\uD83C\uDDF7\uD83C\uDDFC"),
    LanguageData("ca", "Catalan", "Català", "\uD83C\uDDE8\uD83C\uDDF2"),
    LanguageData("cs", "Czech", "Čeština", "\uD83C\uDDE8\uD83C\uDDF3"),
    LanguageData("da", "Danish", "Dansk", "\uD83C\uDDE9\uD83C\uDDF0"),
    LanguageData("el", "Greek", "Ελληνικά", "\uD83C\uDDF2\uD83C\uDDF4"),
    LanguageData("en-gb", "English (UK)", null, "\uD83C\uDDE7\uD83C\uDDF7"),
    LanguageData("et", "Estonian", "Eesti", "\uD83C\uDDE9\uD83C\uDDF9"),
    LanguageData("eu", "Basque", "Euskara", "\uD83C\uDDEA\uD83C\uDDF8"),
    LanguageData("fa", "Persian", "فارسی", "\uD83C\uDDF8\uD83C\uDDFC"),
    LanguageData("fi", "Finnish", "Suomi", "\uD83C\uDDF5\uD83C\uDDFB"),
    LanguageData("hr", "Croatian", "Hrvatski", "\uD83C\uDDF7\uD83C\uDDFC"),
    LanguageData("hu", "Hungarian", "Magyar", "\uD83C\uDDF1\uD83C\uDDFB"),
    LanguageData("hy", "Armenian", "Հայերեն", "\uD83C\uDDF7\uD83C\uDDF0"),
    LanguageData("is", "Icelandic", "Íslenska", "\uD83C\uDDF8\uD83C\uDDFB"),
    LanguageData("it", "Italian", "Italiano", "\uD83C\uDDF2\uD83C\uDDF5"),
    LanguageData("iw", "Hebrew", "עברית", "\uD83C\uDDF9\uD83C\uDDF1"),
    LanguageData("km", "Khmer", "ខ្មែរ", "\uD83C\uDDF0\uD83C\uDDF2"),
    LanguageData("kn", "Kannada", "ಕನ್ನಡ", "\uD83C\uDDEA\uD83C\uDDF2"),
    LanguageData("ko", "Korean", "한국어", "\uD83C\uDDEA\uD83C\uDDF0"),
    LanguageData("lo", "Lao", "ລາວ", "\uD83C\uDDF2\uD83C\uDDF4"),
    LanguageData("lt", "Lithuanian", "Lietuvių", "\uD83C\uDDF2\uD83C\uDDF4"),
    LanguageData("lv", "Latvian", "Latviešu", "\uD83C\uDDF2\uD83C\uDDFB"),
    LanguageData("mk", "Macedonian", "Македонски", "\uD83C\uDDF2\uD83C\uDDF0"),
    LanguageData("ml", "Malayalam", "മലയാളം", "\uD83C\uDDF2\uD83C\uDDF2"),
    LanguageData("mn", "Mongolian", "Монгол", "\uD83C\uDDF2\uD83C\uDDF4"),
    LanguageData("ms", "Malay", "Melayu", "\uD83C\uDDF2\uD83C\uDDF2"),
    LanguageData("my", "Burmese", "မြန်မာဘာသာ", "\uD83C\uDDF2\uD83C\uDDFB"),
    LanguageData("nb", "Norwegian", "Norsk", "\uD83C\uDDF3\uD83C\uDDFB"),
    LanguageData("ne", "Nepali", "नेपाली", "\uD83C\uDDF2\uD83C\uDDF4"),
    LanguageData("nl", "Dutch", "Nederlands", "\uD83C\uDDF3\uD83C\uDDFC"),
    LanguageData("pl", "Polish", "Polski", "\uD83C\uDDF5\uD83C\uDDF1"),
    LanguageData("ro", "Romanian", "Română", "\uD83C\uDDF7\uD83C\uDDF4"),
    LanguageData("si", "Sinhala", "සිංහල", "\uD83C\uDDF8\uD83C\uDDF2"),
    LanguageData("sk", "Slovak", "Slovenčina", "\uD83C\uDDF8\uD83C\uDDF3"),
    LanguageData("sl", "Slovenian", "Slovenščina", "\uD83C\uDDF8\uD83C\uDDF3"),
    LanguageData("sr", "Serbian", "Српски", "\uD83C\uDDF8\uD83C\uDDF0"),
    LanguageData("sv", "Swedish", "Svenska", "\uD83C\uDDF8\uD83C\uDDFB"),
    LanguageData("sw", "Swahili", "Kiswahili", "\uD83C\uDDF8\uD83C\uDDFB"),
    LanguageData("th", "Thai", "ไทย", "\uD83C\uDDF9\uD83C\uDDF0"),
    LanguageData("uk", "Ukrainian", "Українська", "\uD83C\uDDF2\uD83C\uDDFB")
)

@Composable
fun SelectLanguageHeader(
    onDismiss: () -> Unit
) {
    AppColumn(modifier = Modifier.fillMaxWidth()) {
        AppBox(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            // Title
            AppText(
                text = stringResource(R.string.select_language),
                color = AppColors.current.text,
                fontSize = AppFontSize.BODY1,
                fontWeight = FontWeight.W700,
                lineHeight = 24.sp,
                modifier = Modifier.align(Alignment.Center)
            )

            AppText(
                text = stringResource(R.string.txtid_close),
                color = Color(0xFF4BA1FF),//Color
                fontSize = AppFontSize.BODY1,
                fontWeight = FontWeight.W400,
                lineHeight = 24.sp,
                modifier = Modifier.align(Alignment.CenterEnd).clickable { onDismiss() }.padding(end = 16.dp)
            )
        }
    }
}