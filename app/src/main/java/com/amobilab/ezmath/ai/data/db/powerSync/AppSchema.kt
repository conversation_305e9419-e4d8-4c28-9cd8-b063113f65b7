package com.amobilab.ezmath.ai.data.db.powerSync

import com.powersync.db.schema.*

val AppSchema = Schema(
    listOf(

        // Bảng user
        Table(
            name = "users",
            columns = listOf(
                Column.integer("coin"), // TEXT cho String
                Column.integer("is_deleted"),
            ),
        ),

        // Bảng coin_history_table
        Table(
            name = "coin_history_table",
            columns = listOf(
                Column.text("type"),            // TEXT cho TransactionType (EARN/SPEND)
                Column.integer("amount"),       // INTEGER cho Int
                Column.integer("date"),         // INTEGER cho Long
                Column.text("description")      // TEXT cho String
            ),
//            indexes = listOf(
//                Index(
//                    name = "idx_coin_history_date",
//                    columns = listOf(IndexedColumn.descending("date")) // Tối ưu sắp xếp theo ngày
//                )
//            )
        ),
        // Bảng chat_table
        Table(
            name = "chat_table",
            columns = listOf(
                Column.text("history_id"),    // INTEGER cho Long (khóa ngoại)
                Column.integer("timestamp"),     // INTEGER cho Long
                Column.text("content"),          // TEXT cho String
                Column.integer("is_human"),      // INTEGER cho Boolean (0/1)
                Column.integer("is_error"),      // INTEGER cho Boolean (0/1)
                Column.text("image_data"),       // TEXT cho ByteArray? (Base64)
                Column.text("bot_name")          // TEXT cho String? (có thể null)
            ),
//            indexes = listOf(
//                Index(
//                    name = "idx_chat_list_id",
//                    columns = listOf(IndexedColumn.ascending("id"))
//                ),
//                Index(
//                    name = "idx_chat_history_id",
//                    columns = listOf(IndexedColumn.ascending("history_id")) // Tối ưu truy vấn khóa ngoại
//                ),
//                Index(
//                    name = "idx_chat_timestamp",
//                    columns = listOf(IndexedColumn.descending("timestamp")) // Tối ưu sắp xếp theo thời gian
//                )
//            )
        ),
        // Bảng history_table
        Table(
            name = "history_table",
            columns = listOf(
                Column.integer("timestamp"),     // INTEGER cho Long
                Column.text("history_name"),     // TEXT cho String
                Column.integer("is_favorite"),   // INTEGER cho Boolean (0/1)
                Column.text("content"),          // TEXT cho String
                Column.text("image_data"),       // TEXT cho ByteArray? (Base64)
                Column.text("question_mode"),    // TEXT cho String
                Column.text("model_ai_chat")     // TEXT cho String
            ),
//            indexes = listOf(
//                Index(
//                    name = "idx_history_id",
//                    columns = listOf(IndexedColumn.ascending("id"))
//                ),
//                Index(
//                    name = "idx_history_timestamp",
//                    columns = listOf(IndexedColumn.descending("timestamp")) // Tối ưu sắp xếp theo thời gian
//                ),
//                Index(
//                    name = "idx_history_is_favorite",
//                    columns = listOf(IndexedColumn.ascending("is_favorite")) // Tối ưu lọc lịch sử yêu thích
//                )
//            )
        )
    )
)