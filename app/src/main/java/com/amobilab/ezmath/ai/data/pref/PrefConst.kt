package com.amobilab.ezmath.ai.data.pref

import amobi.module.common.configs.CommFigs
import com.amobilab.ezmath.ai.presentation.common.shared_values.AppThemeMode
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode

object PrefConst {
    const val APP_THEME_MODE = "APP_THEME_MODE"

    const val TRANSLATE_TARGET = "TRANSLATE_TARGET"
    const val MODEL_AI = "MODEL_AI"
    const val SORT_BY = "SORT_BY"
    const val FIRST_TIME_OPEN_APP = "FIRST_TIME_OPEN_APP"
    const val FREE_CHAT = "FREE_CHAT"
    const val FREE_CHAT_SYNCHRONIZED = "FREE_CHAT_SYNCHRONIZED"
    const val ANDROID_ID = "ANDROID_ID"
    const val TOKEN_ID = "TOKEN_ID"
    const val USER_ID = "USER_ID"
    
    const val SHOW_TOOLTIP_TAB_SCAN_COUNTER = "SHOW_TOOLTIP_TAB_SCAN_COUNTER"
    const val SHOW_TOOLTIP_TAB_SCAN_MAX = 11
    const val SHOW_TOOLTIP_TAB_CHAT_COUNTER = "SHOW_TOOLTIP_TAB_CHAT_COUNTER"
    const val SHOW_TOOLTIP_TAB_CHAT_MAX = 11

    const val ONBOARDING_COMPLETED = "ONBOARDING_COMPLETED"


    const val TOTAL_COIN_BALANCE = "TOTAL_COIN_BALANCE"

    const val TOTAL_COIN_BALANCE_TEMP = "TOTAL_COIN_BALANCE_TEMP"

    const val TOTAL_COIN_BALANCE_OLD = "TOTAL_COIN_BALANCE_OLD"


    const val KEY_MIGRATION_ROOM_TO_POWER_SYNC_DONE = "KEY_MIGRATION_ROOM_TO_POWER_SYNC_DONE"



    object Token {
        const val INPUT_TOKEN_COUNT = "inputTokenCount"
        const val OUTPUT_TOKEN_COUNT = "outputTokenCount"
        const val TOTAL_TOKEN_COUNT = "totalTokenCount"
    }


    fun getDefString(key: String): String {
        return when (key) {
            APP_THEME_MODE -> AppThemeMode.SYSTEM.name
            TRANSLATE_TARGET -> "English (US)"
            MODEL_AI -> ModelAiMode.GEMINI.name
            else -> ""
        }
    }

    fun getDefLong(key: String): Long {
        return when (key) {
            else -> 0L
        }
    }


    fun getDefBoolean(key: String): Boolean {
        return when (key) {
            else -> false
        }
    }

    fun getDefInt(key: String): Int {
        return when (key) {
            FREE_CHAT -> if (!CommFigs.IS_SHOW_TEST_OPTION) 3 else 10
            FREE_CHAT_SYNCHRONIZED -> -1
            else -> 0
        }
    }
}