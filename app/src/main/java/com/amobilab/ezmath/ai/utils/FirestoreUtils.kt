package com.amobilab.ezmath.ai.utils

import amobi.module.common.CommApplication
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.RconfAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.debugLog
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.data.pref.RconfConst
import com.google.firebase.firestore.FieldValue
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.SetOptions
import kotlinx.coroutines.tasks.await
import java.util.Locale
import java.util.UUID

object FirestoreUtils {
    private val firestore get() = FirebaseFirestore.getInstance("firestoredata")

    var syncCoinsInterval = 0L

    suspend fun syncCoinsFirestore(): Boolean {
        val userId = PrefAssist.getString(PrefConst.USER_ID)
        if (userId.isEmpty())
            return false

        return try {
            // Lấy số coin khởi tạo từ PrefAssist
            val totalCoins = PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE)
            val userCoinsRef = firestore.collection("users_coin").document(userId)

            // Kiểm tra xem tài liệu có tồn tại không
            val document = userCoinsRef.get().await()
            if (!document.exists()) {
                userCoinsRef.set(mapOf("coins" to totalCoins)).await()
                return true
            }

            val totalCoinsOnFirestore = document.getLong("coins") ?: 0L
            if (totalCoinsOnFirestore != 0L && totalCoinsOnFirestore < totalCoins) {
                userCoinsRef.set(mapOf("coins" to totalCoins)).await()
                return true
            } else {
                PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE, totalCoinsOnFirestore)
                return true
            }

            userCoinsRef.set(mapOf("coins" to totalCoins)).await()

            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    // Tạo coin cho người dùng mới
    suspend fun createCoinsForUser(onFinish: (Long) -> Unit): Boolean {
        val userId = PrefAssist.getString(PrefConst.USER_ID)
        if (userId.isEmpty())
            return false

        return try {
            // Lấy số coin khởi tạo từ PrefAssist
            var initialCoins = PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE)
            val userCoinsRef = firestore.collection("users_coin").document(userId)

            // Kiểm tra xem tài liệu có tồn tại không
            val document = userCoinsRef.get().await()
            if (!document.exists()) {
                // Nếu tài liệu không tồn tại, tạo mới với số coin khởi tạo
                initialCoins += RconfAssist.getInt(RconfConst.CREDIT_SIGN_IN_REWARD).toLong()
                val coinString = String.format(Locale.getDefault(), "%,d", initialCoins)
                val context = CommApplication.appContext
                MixedUtils.showToast(context, context.getString(R.string.txtid_coins) + ": +$coinString")
                userCoinsRef.set(mapOf("coins" to initialCoins)).await()

                onFinish(initialCoins)
            } else {
                // Nếu tài liệu tồn tại, công thêm số coin khởi tạo
                userCoinsRef.update("coins", FieldValue.increment(initialCoins)).await()

                // Lấy số coin hiện tại từ dữ liệu
                val updatedDocument = userCoinsRef.get().await()
                val currentCoins = updatedDocument.getLong("coins") ?: 0L // Lấy số coin hiện tại

                onFinish(currentCoins)
                // Cập nhật TOTAL_COIN_COUNT theo số coin hiện tại
            }

            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    // Cập nhật số coin cho người dùng
    suspend fun updateCoinsForUser(userId: String, newCoinCount: Long): Boolean {
        return try {
            val userCoinsRef = firestore.collection("users_coin").document(userId)

            // Cập nhật số coin bằng giá trị mới
            userCoinsRef.set(mapOf("coins" to newCoinCount), SetOptions.merge()).await()

            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }


    // Cập nhật số coin cho người dùng
    suspend fun deleteCoinsForUser(userId: String): Boolean {
        return try {
            // Delete user document from Firestore
            val userDocRef = firestore.collection("users_coin").document(userId)
            userDocRef.delete().await()
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    suspend fun checkUserDocumentExists(userId: String): Boolean {
        return try {
            val userDocRef = firestore.collection("users_coin").document(userId)
            val document = userDocRef.get().await()
            return document.exists()
        } catch (e: Exception) {
            e.printStackTrace()
            false // Lỗi trong quá trình kiểm tra
        }
    }

    suspend fun updateFreeChatUsage(freeChat: Int): Int {
        var androidId = PrefAssist.getString(PrefConst.ANDROID_ID)
        if (androidId.isEmpty())
            androidId = UniqueDeviceID.getUniqueId() ?: return 0

        debugLog(" ${PrefConst.ANDROID_ID} $androidId")
        PrefAssist.setString(PrefConst.ANDROID_ID, androidId)

        PrefAssist.setInt(PrefConst.FREE_CHAT, freeChat)

        try {
            val userCoinsRef = firestore.collection("free_chat").document(androidId)
            val document = userCoinsRef.get().await()

            val freeUsage = document.getLong("free_turn")?.toInt() ?: freeChat

            if (freeUsage < freeChat) {
                PrefAssist.setInt(PrefConst.FREE_CHAT, freeUsage)
                PrefAssist.setInt(PrefConst.FREE_CHAT_SYNCHRONIZED, freeUsage)
                return freeUsage
            }

            if (freeChat == PrefAssist.getInt(PrefConst.FREE_CHAT_SYNCHRONIZED)) {
                return freeChat
            }
            PrefAssist.setInt(PrefConst.FREE_CHAT_SYNCHRONIZED, freeChat)

            userCoinsRef.set(mapOf("free_turn" to freeChat), SetOptions.merge()).await()

            return freeChat
        } catch (e: Exception) {
            e.printStackTrace()
            return freeChat
        }
    }
}