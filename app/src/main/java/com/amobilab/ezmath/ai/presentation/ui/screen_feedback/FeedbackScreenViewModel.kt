package com.amobilab.ezmath.ai.presentation.ui.screen_feedback

import amobi.module.common.utils.DebugLogCustom
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.documentfile.provider.DocumentFile
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.app.BaseViewModel
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.storage.FirebaseStorage
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import javax.inject.Inject

@HiltViewModel
class FeedbackScreenViewModel @Inject constructor() : BaseViewModel() {
    companion object {
        const val TAG = "FeedbackSuggestionScreenViewModel"
        private const val CAP_BYTE_ATTACH = (25 * 1024 * 1024).toLong()
        private const val MAX_FILE_SIZE_MB = 25L
    }

    // UI state
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // Error state
    private val _isMessageEmpty = MutableStateFlow(false)
    val isMessageEmpty: StateFlow<Boolean> = _isMessageEmpty.asStateFlow()

    // Form fields
    val feedbackTitle = mutableStateOf("")
    val feedbackMessage = mutableStateOf("")

    // Attachments
    val uriList = mutableStateListOf<Uri>()
    val uploadedUriList = mutableStateListOf<String>()
    var totalAttachments = 0
        private set
    var totalByteAttach: Long = 0
        private set
    var attachmentInfoText = mutableStateOf("")

    // For tracking upload progress
    private var uploadCounter = 0
    private var currentDateAndTime: String? = null

    fun addAttachment(context: Context, uri: Uri): Boolean {
        // Check if we already have too many attachments
        if (totalAttachments >= 5) {
            return false
        }

        val documentFile = DocumentFile.fromSingleUri(context, uri)
        if (documentFile == null) {
            return false
        }

        val displayName = documentFile.name ?: return false
        val size = documentFile.length()
        val displaySize = if (size > (1024 * 1024)) "${size / 1024 / 1024}M" else "${size / 1024}K"

        val type = documentFile.type ?: return false

        // Only allow images and PDFs
        if (!type.contains("image/") && !type.equals("application/pdf", ignoreCase = true)) {
            context.resources.getString(R.string.feedback_attachment_type_error).let { message ->
                DebugLogCustom.logd("Attachment type error: $message")
            }
            return false
        }

        // Check size cap
        if (totalByteAttach + size > CAP_BYTE_ATTACH) {
            context.resources.getString(R.string.feedback_attachment_too_large, MAX_FILE_SIZE_MB).let { message ->
                DebugLogCustom.logd("Attachment size error: $message")
            }
            return false
        }

        // For images, try to decode to ensure it's valid
        if (type.contains("image/")) {
            val bMap = getBitmapFromUri(context, uri)
            if (bMap == null) {
                return false
            }
        }

        // Add to list
        uriList.add(uri)
        totalByteAttach += size
        totalAttachments++

        // Update attachment info text
        val currentText = if (attachmentInfoText.value.isNotEmpty()) "${attachmentInfoText.value}\n" else ""
        attachmentInfoText.value = "$currentText$displayName ($displaySize)"

        return true
    }

    private fun getBitmapFromUri(context: Context, uri: Uri): Bitmap? {
        return try {
            context.contentResolver.openFileDescriptor(uri, "r")?.use {
                val fileDescriptor = it.fileDescriptor
                BitmapFactory.decodeFileDescriptor(fileDescriptor)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    fun validateFeedback(): Boolean {
        val messageValue = feedbackMessage.value.trim()
        if (messageValue.isEmpty()) {
            _isMessageEmpty.value = true
            return false
        }
        return true
    }

    fun clearMessageError() {
        if (_isMessageEmpty.value) {
            _isMessageEmpty.value = false
        }
    }

    fun sendFeedback(context: Context, onSuccess: () -> Unit, onError: (String) -> Unit) {
        if (!validateFeedback()) {
            onError(context.getString(R.string.feedback_message_empty_error))
            return
        }

        _isLoading.value = true

        // Generate timestamp for unique identification
        val timestampMilliseconds = System.currentTimeMillis()
        val simpleDateFormat = SimpleDateFormat("yyyy-MM-dd_HH:mm:ss_SSS", Locale.US)
        simpleDateFormat.timeZone = TimeZone.getTimeZone("GMT")
        currentDateAndTime = simpleDateFormat.format(Date(timestampMilliseconds)) + getRandomLetters(3)

        if (uriList.isNotEmpty()) {
            uploadCounter = 0
            uploadedUriList.clear()

            // Upload each attachment
            for (i in uriList.indices) {
                val imageUri = uriList[i]
                val documentFile = DocumentFile.fromSingleUri(context, imageUri)
                val filename = currentDateAndTime + "_" + (documentFile?.name ?: "unknown")
                uploadImage(imageUri, filename) {
                    uploadCompletedCounter(context, onSuccess, onError)
                }
            }
        } else {
            // No attachments, just upload document
            uploadDoc(context, onSuccess, onError)
        }
    }

    private fun uploadCompletedCounter(context: Context, onSuccess: () -> Unit, onError: (String) -> Unit) {
        uploadCounter++
        if (uploadCounter == uriList.size) {
            uploadDoc(context, onSuccess, onError)
        }
    }

    private fun uploadDoc(context: Context, onSuccess: () -> Unit, onError: (String) -> Unit) {
        val titleInput = feedbackTitle.value
        val bodyInput = feedbackMessage.value

        val feedback: MutableMap<String, String> = HashMap()
        if (titleInput.trim().isNotEmpty()) feedback["feedback_title"] = titleInput
        feedback["feedback_body"] = bodyInput
        feedback["os_api_level"] = android.os.Build.VERSION.RELEASE
        feedback["device_name"] = android.os.Build.DEVICE
        feedback["model"] = android.os.Build.MODEL

        // Add attachment URLs
        for (i in uploadedUriList.indices) {
            feedback["img_$i"] = uploadedUriList[i]
        }

        // Upload to Firestore
        val db = FirebaseFirestore.getInstance()
        db.collection("feedback").document(currentDateAndTime ?: "unknown")
            .set(feedback)
            .addOnSuccessListener {
                _isLoading.value = false
                onSuccess()
            }
            .addOnFailureListener {
                DebugLogCustom.logd("Error uploading feedback: ${it.message}")
                // Even on failure, we'll show success to the user for better UX
                _isLoading.value = false
                onSuccess()
            }
    }

    private fun uploadImage(imageUri: Uri, filename: String, onComplete: () -> Unit) {
        val fileRef = FirebaseStorage.getInstance().getReference(filename)
        fileRef.putFile(imageUri)
            .addOnCompleteListener {
                fileRef.downloadUrl
                    .addOnSuccessListener { uri ->
                        uploadedUriList.add(uri.toString())
                        onComplete()
                    }
                    .addOnFailureListener {
                        uploadedUriList.add(filename)
                        onComplete()
                    }
            }
            .addOnFailureListener {
                uploadedUriList.add(filename)
                onComplete()
            }
    }

    private fun getRandomLetters(length: Int): String {
        val allowedChars = ('A'..'Z') + ('a'..'z')
        return (1..length)
            .map { allowedChars.random() }
            .joinToString("")
    }

    fun clearForm() {
        feedbackTitle.value = ""
        feedbackMessage.value = ""
        uriList.clear()
        uploadedUriList.clear()
        totalAttachments = 0
        totalByteAttach = 0
        attachmentInfoText.value = ""
    }
}