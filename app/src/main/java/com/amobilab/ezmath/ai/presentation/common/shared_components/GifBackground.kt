package com.amobilab.ezmath.ai.presentation.common.shared_components

import amobi.module.compose.foundation.GifBasedOnGlideCompose
import androidx.annotation.DrawableRes
import androidx.annotation.RawRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.core.net.toUri
import kotlinx.coroutines.delay

/**
 * Hiển thị ảnh GIF dưới dạng background với các tùy chọn
 *
 * @param gifResId Resource ID của file GIF
 * @param modifier Modifier để tùy chỉnh layout
 * @param backgroundImageResId Resource ID của hình ảnh nền (hiển thị khi GIF chưa load xong)
 * @param backgroundColor Màu nền mặc định
 * @param isLoop Có lặp lại GIF không
 * @param onGifAnimationEnd Callback khi animation GIF kết thúc
 */
@Composable
fun GifBackground(
    @RawRes gifResId: Int,
    modifier: Modifier = Modifier,
    @DrawableRes backgroundImageResId: Int? = null,
    backgroundColor: Color = Color.Black,
    isLoop: Boolean = true,
    onGifAnimationEnd: (() -> Unit)? = null
) {
    val context = LocalContext.current
    val gifUri = "android.resource://${context.packageName}/$gifResId"

    var isGifReady by remember { mutableStateOf(false) }

    // Delay hiển thị GIF 300ms để tạo hiệu ứng loading mượt mà
    LaunchedEffect(Unit) {
        delay(300)
        isGifReady = true
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(backgroundColor)
    ) {
        // Hiển thị hình ảnh nền khi GIF chưa load xong
        if (backgroundImageResId != null) {
            Image(
                painter = painterResource(id = backgroundImageResId),
                contentDescription = null,
                contentScale = ContentScale.Crop,
                modifier = Modifier.fillMaxSize()
            )
        }

        // Hiển thị GIF khi đã sẵn sàng
        if (isGifReady) {
            GifBasedOnGlideCompose(
                modifier = Modifier.fillMaxSize(),
                imageSource = gifUri.toUri(),
                backgroundColor = Color.Transparent,
                isLoop = isLoop,
                onGifAnimationEnd = onGifAnimationEnd
            )
        }
    }
}

/**
 * Hiển thị ảnh GIF từ URL dưới dạng background với các tùy chọn
 *
 * @param gifUrl URL của file GIF
 * @param modifier Modifier để tùy chỉnh layout
 * @param backgroundImageResId Resource ID của hình ảnh nền (hiển thị khi GIF chưa load xong)
 * @param backgroundColor Màu nền mặc định
 * @param isLoop Có lặp lại GIF không
 * @param onGifAnimationEnd Callback khi animation GIF kết thúc
 */
@Composable
fun GifBackgroundFromUrl(
    gifUrl: String,
    modifier: Modifier = Modifier,
    @DrawableRes backgroundImageResId: Int? = null,
    backgroundColor: Color = Color.Black,
    isLoop: Boolean = true,
    onGifAnimationEnd: (() -> Unit)? = null
) {
    var isGifReady by remember { mutableStateOf(false) }

    // Delay hiển thị GIF 300ms để tạo hiệu ứng loading mượt mà
    LaunchedEffect(Unit) {
        delay(300)
        isGifReady = true
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(backgroundColor)
    ) {
        // Hiển thị hình ảnh nền khi GIF chưa load xong
        if (backgroundImageResId != null) {
            Image(
                painter = painterResource(id = backgroundImageResId),
                contentDescription = null,
                contentScale = ContentScale.Crop,
                modifier = Modifier.fillMaxSize()
            )
        }

        // Hiển thị GIF khi đã sẵn sàng
        if (isGifReady) {
            GifBasedOnGlideCompose(
                modifier = Modifier.fillMaxSize(),
                imageSource = gifUrl,
                backgroundColor = Color.Transparent,
                isLoop = isLoop,
                onGifAnimationEnd = onGifAnimationEnd
            )
        }
    }
}