package com.amobilab.ezmath.ai.data.sync

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.debugLog
import com.amobilab.ezmath.ai.data.db.AppDatabase
import com.amobilab.ezmath.ai.data.firebase.FirebaseCoinRepository
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.google.firebase.auth.FirebaseAuth
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch

/**
 * Manager để đồng bộ coin giữa PowerSync và Firebase
 * Tự động fallback sang Firebase khi PowerSync gặp lỗi
 */
class CoinSyncManager {
    
    private val firebaseCoinRepository = FirebaseCoinRepository()
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // Trạng thái sync
    private val _syncStatus = MutableStateFlow(SyncStatus.IDLE)
    val syncStatus = _syncStatus.asStateFlow()
    
    // Trạng thái PowerSync
    private val _powerSyncStatus = MutableStateFlow(PowerSyncStatus.UNKNOWN)
    val powerSyncStatus = _powerSyncStatus.asStateFlow()
    
    companion object {
        private var instance: CoinSyncManager? = null
        
        fun getInstance(): CoinSyncManager {
            return instance ?: synchronized(this) {
                instance ?: CoinSyncManager().also { instance = it }
            }
        }
        
        private const val SYNC_INTERVAL_MS = 30_000L // 30 giây
        private const val BACKUP_INTERVAL_MS = 60_000L // 1 phút
    }
    
    private var syncJob: Job? = null
    private var backupJob: Job? = null
    
    /**
     * Bắt đầu auto sync
     */
    fun startAutoSync() {
        debugLog("CoinSyncManager: Starting auto sync")
        
        // Dừng job cũ nếu có
        stopAutoSync()
        
        // Bắt đầu sync job
        syncJob = scope.launch {
            while (isActive) {
                try {
                    performSync()
                    delay(SYNC_INTERVAL_MS)
                } catch (e: Exception) {
                    debugLog("CoinSyncManager: Auto sync error: ${e.message}")
                    delay(SYNC_INTERVAL_MS)
                }
            }
        }
        
        // Bắt đầu backup job
        backupJob = scope.launch {
            while (isActive) {
                try {
                    performBackup()
                    delay(BACKUP_INTERVAL_MS)
                } catch (e: Exception) {
                    debugLog("CoinSyncManager: Auto backup error: ${e.message}")
                    delay(BACKUP_INTERVAL_MS)
                }
            }
        }
    }
    
    /**
     * Dừng auto sync
     */
    fun stopAutoSync() {
        debugLog("CoinSyncManager: Stopping auto sync")
        syncJob?.cancel()
        backupJob?.cancel()
        syncJob = null
        backupJob = null
    }
    
    /**
     * Thực hiện sync thủ công
     */
    suspend fun performSync(): Boolean = withContext(Dispatchers.IO) {
        try {
            if (!isUserLoggedIn()) {
                debugLog("CoinSyncManager: User not logged in, skipping sync")
                return@withContext false
            }
            
            _syncStatus.value = SyncStatus.SYNCING
            
            // Kiểm tra trạng thái PowerSync
            val powerSyncWorking = checkPowerSyncStatus()
            _powerSyncStatus.value = if (powerSyncWorking) PowerSyncStatus.CONNECTED else PowerSyncStatus.DISCONNECTED
            
            if (powerSyncWorking) {
                // PowerSync hoạt động bình thường, sync với Firebase
                val powerSyncCoin = getPowerSyncCoin()
                val firebaseCoin = firebaseCoinRepository.getCoinFromFirebase()
                
                debugLog("CoinSyncManager: PowerSync coin: $powerSyncCoin, Firebase coin: $firebaseCoin")
                
                // Sử dụng coin cao hơn
                val finalCoin = maxOf(powerSyncCoin, firebaseCoin)
                
                if (finalCoin != powerSyncCoin) {
                    updatePowerSyncCoin(finalCoin)
                }
                
                if (finalCoin != firebaseCoin) {
                    firebaseCoinRepository.updateCoinToFirebase(finalCoin, "powersync_sync")
                }
                
                updateLocalCoin(finalCoin)
                _syncStatus.value = SyncStatus.SUCCESS
                
            } else {
                // PowerSync không hoạt động, sử dụng Firebase
                debugLog("CoinSyncManager: PowerSync not working, using Firebase fallback")
                val firebaseCoin = firebaseCoinRepository.restoreCoinFromFirebase()
                _syncStatus.value = SyncStatus.FALLBACK_TO_FIREBASE
            }
            
            return@withContext true
            
        } catch (e: Exception) {
            debugLog("CoinSyncManager: Sync error: ${e.message}")
            _syncStatus.value = SyncStatus.ERROR
            return@withContext false
        }
    }
    
    /**
     * Backup coin lên Firebase
     */
    private suspend fun performBackup(): Boolean = withContext(Dispatchers.IO) {
        try {
            if (!isUserLoggedIn()) return@withContext false
            
            val success = firebaseCoinRepository.backupCoinToFirebase()
            debugLog("CoinSyncManager: Backup result: $success")
            return@withContext success
            
        } catch (e: Exception) {
            debugLog("CoinSyncManager: Backup error: ${e.message}")
            return@withContext false
        }
    }
    
    /**
     * Cập nhật coin với fallback mechanism
     */
    suspend fun updateCoinWithFallback(newCoin: Long, description: String): Boolean = withContext(Dispatchers.IO) {
        try {
            var success = false
            
            // Thử cập nhật PowerSync trước
            if (checkPowerSyncStatus()) {
                try {
                    updatePowerSyncCoin(newCoin)
                    success = true
                    debugLog("CoinSyncManager: Updated PowerSync coin: $newCoin")
                } catch (e: Exception) {
                    debugLog("CoinSyncManager: PowerSync update failed: ${e.message}")
                }
            }
            
            // Luôn backup lên Firebase
            val firebaseSuccess = firebaseCoinRepository.updateCoinToFirebase(newCoin, "direct_update")
            if (!success && firebaseSuccess) {
                success = true
                debugLog("CoinSyncManager: Fallback to Firebase successful")
            }
            
            // Cập nhật local
            updateLocalCoin(newCoin)
            
            return@withContext success
            
        } catch (e: Exception) {
            debugLog("CoinSyncManager: Update coin error: ${e.message}")
            return@withContext false
        }
    }
    
    /**
     * Lấy coin hiện tại với fallback
     */
    suspend fun getCurrentCoinWithFallback(): Long = withContext(Dispatchers.IO) {
        try {
            // Thử lấy từ PowerSync trước
            if (checkPowerSyncStatus()) {
                val powerSyncCoin = getPowerSyncCoin()
                if (powerSyncCoin > 0) {
                    return@withContext powerSyncCoin
                }
            }
            
            // Fallback sang Firebase
            val firebaseCoin = firebaseCoinRepository.getCoinFromFirebase()
            if (firebaseCoin > 0) {
                updateLocalCoin(firebaseCoin)
                return@withContext firebaseCoin
            }
            
            // Cuối cùng lấy từ local
            return@withContext PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE, 0L)
            
        } catch (e: Exception) {
            debugLog("CoinSyncManager: Get coin error: ${e.message}")
            return@withContext PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE, 0L)
        }
    }
    
    // Helper methods
    private suspend fun checkPowerSyncStatus(): Boolean {
        return try {
            val userRepo = AppDatabase.getInstance().getUserRepository()
            userRepo.getCoin() // Thử lấy coin để test connection
            true
        } catch (e: Exception) {
            debugLog("CoinSyncManager: PowerSync check failed: ${e.message}")
            false
        }
    }
    
    private suspend fun getPowerSyncCoin(): Long {
        return try {
            AppDatabase.getInstance().getUserRepository().getCoin()
        } catch (e: Exception) {
            debugLog("CoinSyncManager: Get PowerSync coin failed: ${e.message}")
            0L
        }
    }
    
    private suspend fun updatePowerSyncCoin(coin: Long) {
        try {
            AppDatabase.getInstance().getUserRepository().updateCoin(coin)
        } catch (e: Exception) {
            debugLog("CoinSyncManager: Update PowerSync coin failed: ${e.message}")
            throw e
        }
    }
    
    private fun updateLocalCoin(coin: Long) {
        PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE, coin)
    }
    
    private fun isUserLoggedIn(): Boolean {
        return FirebaseAuth.getInstance().currentUser != null
    }
}

enum class SyncStatus {
    IDLE,
    SYNCING,
    SUCCESS,
    ERROR,
    FALLBACK_TO_FIREBASE
}

enum class PowerSyncStatus {
    UNKNOWN,
    CONNECTED,
    DISCONNECTED
}
