package com.amobilab.ezmath.ai.presentation.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.app.CoinViewModel
import com.amobilab.ezmath.ai.data.sync.PowerSyncStatus
import com.amobilab.ezmath.ai.data.sync.SyncStatus
import com.amobilab.ezmath.ai.presentation.common.shared_values.AppColors

/**
 * Component hiển thị trạng thái sync coin giữa PowerSync và Firebase
 */
@Composable
fun CoinSyncStatusIndicator(
    modifier: Modifier = Modifier,
    coinViewModel: CoinViewModel = hiltViewModel()
) {
    val syncManager = remember { com.amobilab.ezmath.ai.data.sync.CoinSyncManager.getInstance() }
    val syncStatus by syncManager.syncStatus.collectAsState()
    val powerSyncStatus by syncManager.powerSyncStatus.collectAsState()
    
    // Chỉ hiển thị khi có vấn đề hoặc đang sync
    if (syncStatus == SyncStatus.IDLE && powerSyncStatus == PowerSyncStatus.CONNECTED) {
        return
    }
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = getStatusColor(syncStatus, powerSyncStatus).copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Icon trạng thái
            Icon(
                painter = painterResource(id = getStatusIcon(syncStatus, powerSyncStatus)),
                contentDescription = null,
                tint = getStatusColor(syncStatus, powerSyncStatus),
                modifier = Modifier.size(16.dp)
            )
            
            // Text trạng thái
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = getStatusTitle(syncStatus, powerSyncStatus),
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = getStatusColor(syncStatus, powerSyncStatus)
                )
                
                val description = getStatusDescription(syncStatus, powerSyncStatus)
                if (description.isNotEmpty()) {
                    Text(
                        text = description,
                        fontSize = 10.sp,
                        color = AppColors.current.textSecondary
                    )
                }
            }
            
            // Loading indicator khi đang sync
            if (syncStatus == SyncStatus.SYNCING) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp,
                    color = getStatusColor(syncStatus, powerSyncStatus)
                )
            }
            
            // Nút retry khi có lỗi
            if (syncStatus == SyncStatus.ERROR) {
                TextButton(
                    onClick = { coinViewModel.performManualSync() },
                    modifier = Modifier.height(24.dp),
                    contentPadding = PaddingValues(horizontal = 8.dp, vertical = 0.dp)
                ) {
                    Text(
                        text = stringResource(R.string.retry),
                        fontSize = 10.sp,
                        color = getStatusColor(syncStatus, powerSyncStatus)
                    )
                }
            }
        }
    }
}

@Composable
private fun getStatusColor(syncStatus: SyncStatus, powerSyncStatus: PowerSyncStatus): Color {
    return when {
        syncStatus == SyncStatus.ERROR -> Color(0xFFE53E3E)
        syncStatus == SyncStatus.FALLBACK_TO_FIREBASE -> Color(0xFFED8936)
        syncStatus == SyncStatus.SYNCING -> Color(0xFF3182CE)
        syncStatus == SyncStatus.SUCCESS -> Color(0xFF38A169)
        powerSyncStatus == PowerSyncStatus.DISCONNECTED -> Color(0xFFED8936)
        else -> AppColors.current.textSecondary
    }
}

private fun getStatusIcon(syncStatus: SyncStatus, powerSyncStatus: PowerSyncStatus): Int {
    return when {
        syncStatus == SyncStatus.ERROR -> R.drawable.ic_error
        syncStatus == SyncStatus.FALLBACK_TO_FIREBASE -> R.drawable.ic_warning
        syncStatus == SyncStatus.SYNCING -> R.drawable.ic_sync
        syncStatus == SyncStatus.SUCCESS -> R.drawable.ic_check
        powerSyncStatus == PowerSyncStatus.DISCONNECTED -> R.drawable.ic_warning
        else -> R.drawable.ic_info
    }
}

@Composable
private fun getStatusTitle(syncStatus: SyncStatus, powerSyncStatus: PowerSyncStatus): String {
    return when {
        syncStatus == SyncStatus.ERROR -> stringResource(R.string.sync_error)
        syncStatus == SyncStatus.FALLBACK_TO_FIREBASE -> stringResource(R.string.using_firebase_backup)
        syncStatus == SyncStatus.SYNCING -> stringResource(R.string.syncing_coins)
        syncStatus == SyncStatus.SUCCESS -> stringResource(R.string.sync_successful)
        powerSyncStatus == PowerSyncStatus.DISCONNECTED -> stringResource(R.string.powersync_disconnected)
        else -> ""
    }
}

@Composable
private fun getStatusDescription(syncStatus: SyncStatus, powerSyncStatus: PowerSyncStatus): String {
    return when {
        syncStatus == SyncStatus.ERROR -> stringResource(R.string.sync_error_description)
        syncStatus == SyncStatus.FALLBACK_TO_FIREBASE -> stringResource(R.string.firebase_backup_description)
        syncStatus == SyncStatus.SYNCING -> stringResource(R.string.syncing_description)
        powerSyncStatus == PowerSyncStatus.DISCONNECTED -> stringResource(R.string.powersync_disconnected_description)
        else -> ""
    }
}
