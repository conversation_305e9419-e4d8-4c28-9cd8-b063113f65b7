package com.amobilab.ezmath.ai.presentation.ui.testkey

import amobi.module.common.configs.RconfAssist
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppColumnCentered
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppSwitch
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.amobilab.ezmath.ai.data.pref.RconfConst


@Composable
fun ScreenTestKey() {
    // State cho các switch
    var isUseGatewayApi by remember {
        mutableStateOf(RconfAssist.getBoolean(RconfConst.IS_USE_GATEWAY_API))
    }
    var isNotRandomResponse by remember {
        mutableStateOf(RconfAssist.getBoolean(RconfConst.IS_NOT_RANDOM_RESPONSE))
    }

    Scaffold {
        innerPadding ->
        AppColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp),
            verticalArrangement = Arrangement.Top
        ) {
            // Tiêu đề
            AppText(
                text = "Test Configuration",
                fontSize = AppFontSize.TITLE,
                fontWeight = FontWeight.Bold,
                color = AppColors.current.titleContent,
                modifier = Modifier.padding(bottom = 24.dp)
            )

            // Gateway API Switch
            AppColumnCentered(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        AppColors.current.backgroundContent,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .padding(16.dp)
            ) {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AppColumn(
                        modifier = Modifier.weight(1f),
                        horizontalAlignment = Alignment.Start
                    ) {
                        AppText(
                            text = "Use Gateway API",
                            fontSize = AppFontSize.BODY1,
                            fontWeight = FontWeight.W600,
                            color = AppColors.current.titleContent
                        )
                        AppSpacer(4.dp)
                        AppText(
                            text = "Bật/tắt sử dụng Gateway API",
                            fontSize = AppFontSize.BODY2,
                            color = AppColors.current.text,
                            lineHeight = 16.sp
                        )
                    }

                    AppSwitch(
                        checked = isUseGatewayApi,
                        onCheckedChange = { newValue ->
                            isUseGatewayApi = newValue
                            RconfAssist.setTestBoolean(RconfConst.IS_USE_GATEWAY_API, newValue)
                        }
                    )
                }
            }

            AppSpacer(16.dp)

            // Random Response Switch
            AppColumnCentered(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        AppColors.current.backgroundContent,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .padding(16.dp)
            ) {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AppColumn(
                        modifier = Modifier.weight(1f),
                        horizontalAlignment = Alignment.Start
                    ) {
                        AppText(
                            text = "Not Random Response",
                            fontSize = AppFontSize.BODY1,
                            fontWeight = FontWeight.W600,
                            color = AppColors.current.titleContent
                        )
                        AppSpacer(4.dp)
                        AppText(
                            text = "Bật/tắt không phản hồi ngẫu nhiên",
                            fontSize = AppFontSize.BODY2,
                            color = AppColors.current.text,
                            lineHeight = 16.sp
                        )
                    }

                    AppSwitch(
                        checked = isNotRandomResponse,
                        onCheckedChange = { newValue ->
                            isNotRandomResponse = newValue
                            RconfAssist.setTestBoolean(RconfConst.IS_NOT_RANDOM_RESPONSE, newValue)
                        }
                    )
                }
            }
        }
    }
}